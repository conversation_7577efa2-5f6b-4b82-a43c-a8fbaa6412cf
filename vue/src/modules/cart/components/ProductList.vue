<template>
  <!-- <div class="product-list flex max-h-[400px] flex-col gap-4 overflow-y-auto"> -->
  <div class="product-list flex flex-col gap-4">
    <div
      class="flex w-full animate-pulse flex-col gap-2 rounded-lg border border-solid border-[#B8C2EF] bg-[#E7EBFA] p-3"
      style="animation-iteration-count: 1"
      v-for="item in props.store.items"
      :key="item.id"
    >
      <div class="flex items-center justify-between gap-2">
        <p class="max-w-[70%]">
          {{ props?.store?.getName(item) }}

          <span
            class="block text-sm text-gray-500"
            v-if="props?.store?.getSubName?.(item)"
          >
           {{props?.store?.getSubName?.(item)}}
          </span>
          <span
            class="block text-sm text-gray-500"
            v-if="props?.store?.getDescription?.(item)"
          >
           {{props?.store?.getDescription?.(item)}}
          </span>

          <!-- Korean Top-up Product Type Display -->
          <span
            class="block text-sm text-blue-600 font-medium"
            v-if="item?.options?.type === 'topup_korean' && item?.options?.product_type && item?.options?.package_type === 1"
          >
            {{ getKoreanTopupTypeName(item.options.product_type) }}
          </span>

        </p>
        <div class="flex items-center gap-2">
          <p class="price text-red-500">
            {{ formatCurrency(props.store.getPrice(item)) }}
          </p>
          <button class="text-gray-600" @click="props.store.removeItem(item)">
            <Trash />
          </button>
        </div>
      </div>
      <div class="mt-1 flex items-center justify-between">
        <p class="text-sm text-gray-500" >
          <span v-if="item.discount">
            {{ t('checkout.discount') }} : {{ props.store.getDiscount(item) }}%
          </span>
        </p>
        <div class="flex w-[20%] items-center" v-if="isAllowChangeQuantity">
          <button
            @click="props.store.changeQuantity(item.id, item.quantity - 1)"
            class="flex aspect-square h-5 items-center justify-center rounded-l border border-solid border-gray-400"
          >
            -
          </button>
          <input
            type="text"
            inputmode="numeric"
            @change="(e) => handleAmountChange(item, e)"
            :value="item.quantity"
            class="col-span-1 flex aspect-square h-5 w-full items-center justify-center border border-solid border-gray-400 bg-transparent p-2 text-center text-sm"
          />
          <button
            @click="props.store.changeQuantity(item.id, item.quantity + 1)"
            class="flex aspect-square h-5 items-center justify-center rounded-r border border-solid border-gray-400"
          >
            +
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import Trash from '@/modules/cart/icons/Trash.vue';
import { computed, watch, watchEffect } from 'vue';
import { t } from 'i18next';
import { formatCurrency } from '@/shared/utils';

const props = defineProps(['store']);
const isAllowChangeQuantity = computed(() => props.store.canChangeQuantity());

const handleAmountChange = (item, e) => {
  const value = parseInt(e.target.value);
  const changedQuantity = props.store.changeQuantity(item.id, value);
  e.target.value = changedQuantity
};

// Function to get Korean top-up type name from locales
const getKoreanTopupTypeName = (productType) => {
  // Handle product_type as integer
  const typeMap = {
    1: t('topup_korean.regular_phone'),    // Regular Phone Package
    2: t('topup_korean.special_phone'),    // Special Phone Package
    3: t('topup_korean.data_package')      // Data Package
  };

  return typeMap[productType] || '';
};

watch(() => props.store, (newItems, oldItems) => {
  if (props.store?.type) {
    const currencyId = window.APP_CONFIGS?.currency?.id || 1;
    localStorage.setItem(
        'cart_' + props.store?.type,
        JSON.stringify({
        items: props.store?.items,  
        type: props.store?.type,
        currency_id: currencyId
      })
    );
  } 
}, { deep: true });

</script>
