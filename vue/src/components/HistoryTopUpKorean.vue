<template>
    <div class="col-span-1 md:col-span-8">
        <div class="col-span-1 w-full rounded-xl border-[#1434CB] border border-solid p-4">
            <div class="grid md:grid-cols-12 grid-cols-3 items-center gap-2">
                <div class="col-span-10">
                    <h5 class="text-base font-bold tracking-tight md:text-xl text-[#1434CB] mb-4">
                       {{ t('orders_history.order_history_topup') }}
                    </h5>
                </div>
                <div class="col-span-2 items-right">
                    <!-- <button class="btn-primary w-full"  type="button" @click="exportTopUpHistoryExcel(dataHistory)">
                        <i class="fa fa-file-excel-o" aria-hidden="true"></i> {{ t('orders_history.export_excel') }}
                    </button> -->
                </div>
            </div>
            
            
            <div class="col-span-12">
                <!-- Filter -->
                <form class="form-group grid grid-cols-1 items-center gap-2 md:grid-cols-4">
                    <div class="grid grid-cols-2 items-center gap-1">
                        <input
                            name="keyword"
                            class="form-control mt-0"
                            type="text"
                            :placeholder="t('orders_history.order_code')"
                            v-model="formFilter.keyword"
                        />
                        <input
                            name="account"
                            class="form-control mt-0"
                            type="text"
                            :placeholder="t('subscriber')"
                            v-model="formFilter.account"
                        />
                    </div>
                    <select
                        id="status"
                        class="form-control form-select"
                        v-model="formFilter.status"
                    >
                        <option value="" selected>{{ t('orders_history.all_status') }}</option>
                        <option v-for="order in orderStatus_new" :value="order">
                        {{ t('orderStatus.' + order.toLocaleUpperCase()) }}
                        </option>
                    </select>

                    <div class="grid grid-cols-2 items-center gap-1">
                        <!-- <input
                            name="start_date"
                            class="form-control"
                            type="date"
                            placeholder="Ngày bắt đầu"
                            v-model="formFilter.start_date"
                        />

                        <input
                            name="end_date"
                            class="form-control mt-0"
                            type="date"
                            placeholder="Ngày kết thúc"
                            v-model="formFilter.end_date"
                        /> -->
                         <!-- Custom input start date -->
                        <div class="date-input-wrapper date-input-wrapper-start mt-2">
                            <!-- Input date thật -->
                            <input
                                name="start_date"
                                class="form-control date-input"
                                type="date"
                                :max="formFilter.start_date || today"
                                v-model="formFilter.start_date"
                                :placeholder="t('balance_history.start_date')"
                                @input="handleDateChangeStart"
                            />

                            <!-- Placeholder giả -->
                            <div 
                            v-if="!formFilter.start_date" 
                            class="fake-placeholder"
                            >
                            {{ t('balance_history.start_date') }}
                            </div>

                            <!-- Icon calendar giả -->
                            <div class="calendar-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                                <line x1="16" y1="2" x2="16" y2="6"></line>
                                <line x1="8" y1="2" x2="8" y2="6"></line>
                                <line x1="3" y1="10" x2="21" y2="10"></line>
                            </svg>
                            </div>
                        </div>
                        <!-- END Custom input start date -->

                        <!-- Custom input end date -->
                        <div class="date-input-wrapper date-input-wrapper-end mt-2">
                            <!-- Input date thật -->
                            <input
                                name="end_date"
                                class="form-control date-input"
                                type="date"
                                :min="formFilter.start_date"
                                :max="today"
                                v-model="formFilter.end_date"
                                :placeholder="t('balance_history.end_date')"
                                @input="handleDateChangeEnd"
                            />

                            <!-- Placeholder giả -->
                            <div 
                            v-if="!formFilter.end_date" 
                            class="fake-placeholder"
                            >
                            {{ t('balance_history.end_date') }}
                            </div>

                            <!-- Icon calendar giả -->
                            <div class="calendar-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                                <line x1="16" y1="2" x2="16" y2="6"></line>
                                <line x1="8" y1="2" x2="8" y2="6"></line>
                                <line x1="3" y1="10" x2="21" y2="10"></line>
                            </svg>
                            </div>
                        </div>
                        <!-- END Custom input end date -->
                    </div>

                    <div class="grid grid-cols-2 items-center gap-1">
                        <button class="btn-secondary mb-0 mt-4" type="button" @click="handleSearchHistory">
                            {{ t('orders_history.search') }}
                        </button>
                        <button class="btn-primary w-full mb-0 mt-4"  type="button" @click="exportTopUpHistoryExcel(dataHistory)">
                            <i class="fa fa-file-excel-o" aria-hidden="true"></i> {{ t('orders_history.export_excel') }}
                        </button>
                    </div>
                </form>
            </div>
            <div class="relative overflow-x-auto sm:rounded-lg">

                <div class="table-responsive">
                    <table
                        class="table-responsive w-full p-3 text-left text-base text-gray-900 md:text-base rtl:text-right">
                        <thead class="bg-gray-50 text-gray-700">
                            <tr>

                                <th scope="col"
                                    class=" border-r border-gray-200 p-2 md:border-transparent md:px-6 md:py-3">{{ t('orders_history.invoice_id') }}</th>
                                <th scope="col"
                                    class=" border-r border-gray-200 p-2 md:border-transparent md:px-6 md:py-3">{{ t('deposit.method') }}</th>
                                <th scope="col"
                                    class=" border-r border-gray-200 p-2 md:border-transparent md:px-6 md:py-3">{{ t('orders_history.card_price') }}</th>
                                <th scope="col"
                                    class=" border-r border-gray-200 p-2 md:border-transparent md:px-6 md:py-3">{{ t('orders_history.payment_status') }}</th>

                                <th scope="col"
                                    class=" border-r border-gray-200 p-2 md:border-transparent md:px-6 md:py-3">{{ t('orders_history.order_status') }}</th>

                                <th scope="col"
                                    class=" border-r border-gray-200 p-2 md:border-transparent md:px-6 md:py-3">{{ t('orders_history.ammount') }}
                                    </th>

                                <th scope="col"
                                    class=" border-r border-gray-200 p-2 md:border-transparent md:px-6 md:py-3">{{ t('orders_history.tran_date') }}
                                    </th>

                                <th scope="col"
                                    class=" border-r border-gray-200 p-2 md:border-transparent md:px-6 md:py-3">{{ t('balance_history.view_order') }}
                                </th>

                            </tr>
                        </thead>

                        <tbody>


                            <tr v-for="topup_history in dataHistory?.data" class="border-b border-gray-200 bg-white  md:border-transparent">

                                <td class="border-r border-gray-200 p-2 md:border-transparent md:px-6 md:py-4">{{ topup_history?.id  }}</td>
                                <td class="border-r border-gray-200 p-2 md:border-transparent md:px-6 md:py-4">{{ t('phone_top_up.' + topup_history?.product_type)  }}</td>                                
                                <td class="border-r border-gray-200 p-2 md:border-transparent md:px-6 md:py-4">{{ topup_history?.product_name  }}</td>
                                <td class="border-r border-gray-200 p-2 md:border-transparent md:px-6 md:py-4 " :style="{ color: colorPaymentStatus[topup_history?.status] }">{{ t('paymentStatus.' + topup_history?.status)  }}</td>
                                <td class="border-r border-gray-200 p-2 md:border-transparent md:px-6 md:py-4">
                                    <span v-if="topup_history.completed_orders !== '0'" :style="{ color: colorPaymentStatus['completed_orders'] }">
                                        {{ topup_history.completed_orders  }}
                                        <br>
                                    </span>
                                    <span v-if="topup_history.processing_orders !== '0'"  :style="{ color: colorPaymentStatus['processing_orders'] }">
                                        {{ topup_history.processing_orders  }}
                                        <br>
                                    </span>
                                    <span v-if="topup_history.pending_orders !== '0'"  :style="{ color: colorPaymentStatus['pending_orders'] }">
                                        {{ topup_history.pending_orders  }}
                                        <br>
                                    </span>
                                    <span v-if="topup_history.canceled_orders !== '0'"  :style="{ color: colorPaymentStatus['canceled_orders'] }">
                                        {{ topup_history.canceled_orders  }}
                                        <br>
                                    </span>
                                    <span v-if="topup_history.failed_orders !== '0'"  :style="{ color: colorPaymentStatus['failed_orders'] }">
                                        {{ topup_history.failed_orders  }}
                                        <br>
                                    </span>
                                    <span v-if="topup_history.refunded_orders !== '0'"  :style="{ color: colorPaymentStatus['refunded_orders'] }">
                                        {{ topup_history.refunded_orders  }}
                                        <br>
                                    </span>
                                </td>
                                
                                
                                <td class="border-r border-gray-200 p-2 md:border-transparent md:px-6 md:py-4">{{ topup_history?.payment_net }}</td>
                                <td class="border-r border-gray-200 p-2 md:border-transparent md:px-6 md:py-4">{{ formatDate(topup_history?.created)  }}</td>
                                <td class="border-r border-gray-200 p-2 md:border-transparent md:px-6 md:py-4">
                                    <button class="bg-blue-700 text-white px-4 py-2 rounded" @click="handleRouteToOrderDetail(topup_history)" >{{ t('deposit.view') }}</button>
                                </td>


                            </tr>


                        </tbody>

                    </table>
                </div>

            </div>
        </div>
    </div>
</template>
<script setup>
import { t } from "i18next";
import { getFilterOrderHistory } from "@/modules/form-card/service";
import { useQuery } from "@tanstack/vue-query";
import {formatCurrency} from "@/shared/utils";
import {useAppConfigStore} from "@/stores/app.store";
import { computed } from 'vue';
import * as XLSX from "xlsx";
import { ref } from 'vue';
import {orderStatus, orderStatus_new} from '../modules/orders-history/models/order-history.models';
import extendedDayJs from '@/shared/dayjs';

const appConfigStore = useAppConfigStore()

const props = defineProps(['store_import']);

const formFilter = ref({
    product_type: 'topup_korean',
    account: '',
    status: '',
    start_date: '',
    end_date: '',
    order_code: '',
    is_pc: 1
});

const handleSearchHistory = () => {
    refetch();
}

const colorPaymentStatus = ref({
    paid: '#52C41A',
    unpaid: '#f0ad4e',
    processing_orders: '#f0ad4e',
    completed_orders: '#52C41A',
    pending_orders: '#f0ad4e'
});

const properties = computed(() => {
  let obj = {};

  obj['processing'] = '#f0ad4e';
  obj['completed'] = '#52C41A';
  obj['paid'] = '#52C41A';
  obj['unpaid'] = '#f0ad4e';

  return obj;
});

const { data: dataHistory, isLoading, refetch } = useQuery({
  queryKey: ["getFilterOrderHistory"],
  queryFn: () => {
    // Tạo bản sao của formFilter để không làm thay đổi giá trị gốc
    const filterParams = { ...formFilter.value };
    
    // Chuyển đổi định dạng ngày trong bản sao, không phải giá trị gốc
    filterParams.start_date = filterParams.start_date ? extendedDayJs(filterParams.start_date).startOf('day').unix() : null;
    filterParams.end_date = filterParams.end_date ? extendedDayJs(filterParams.end_date).endOf('day').unix() : null;

    return getFilterOrderHistory(filterParams)
  },
});

function handleRouteToOrderDetail(data) {
  window.location.href =  `/invoice/view/${data.id}.html?token=${data.token}`;
}


const formatTopUpPrice = (price) => {
    if(price > 0){
        return formatCurrency(parseInt(price), appConfigStore?.configs?.vnCurrency);
    }
    return '-';
}

const formatDate = (timestamp) => {
  const date = new Date(parseInt(timestamp) * 1000);

  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0'); // Tháng bắt đầu từ 0
  const year = date.getFullYear();

  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');

  return `${day}-${month}-${year}`;
}

const convertToUpper = (text) => {
  return text?.toUpperCase();
};

const exportTopUpHistoryExcel = (data) => {
    // Tạo phần tử tạm thời với HTML phức tạp
    const tempDiv = document.createElement('div');

    // Nội dung bảng dữ liệu
    tempDiv.innerHTML = `
        <table>
            <tr>
                <th>Mã đơn hàng</th>
                <th>Hình thức</th>
                <th>Mệnh giá</th>
                <th>TT Thanh toán</th>
                <th>TT đơn hàng</th>
                <th>Số tiền</th>
                <th>Ngày GD</th>
            </tr>
            ${data?.data?.map(topup_history => {
                const statusArr = [
                    topup_history?.completed_orders !== "0" ? topup_history?.completed_orders : '',
                    topup_history?.processing_orders !== "0" ? topup_history?.processing_orders : '',
                    topup_history?.pending_orders !== "0" ? topup_history?.pending_orders : '',
                    topup_history?.canceled_orders !== "0" ? topup_history?.canceled_orders : '',
                    topup_history?.failed_orders !== "0" ? topup_history?.failed_orders : '',
                    topup_history?.refunded_orders !== "0" ? topup_history?.refunded_orders : ''
                ];
                const statusStr = statusArr.filter(Boolean).join('. \n');
                return `<tr>
                        <td >${  topup_history?.id  }</td>
                        <td >${  t('phone_top_up.' + topup_history?.product_type)  }</td>
                        <td >${  topup_history?.product_name }</td>
                        <td>${ t('paymentStatus.' + topup_history?.status)  }</td>
                        <td >${ statusStr }</td>
                        <td >${  topup_history?.payment_net }</td>
                        <td >${  formatDate(topup_history?.created)  }</td>
                    </tr>`;
            }).join('')}
        </table>
    `;    

    // Thêm vào DOM nhưng không hiển thị
    tempDiv.style.display = 'none';
    document.body.appendChild(tempDiv);
    
    // Lấy bảng từ tempDiv
    const table = tempDiv.querySelector('table');
    
    // Chuyển đổi bảng HTML thành worksheet
    const worksheet = XLSX.utils.table_to_sheet(table);
    
    // Tùy chỉnh các thuộc tính worksheet
    worksheet['!cols'] = [{ wch: 15 }, { wch: 30 }, { wch: 20 }];
    worksheet['!rows'] = [{ hpt: 30 }]; // Chiều cao dòng tiêu đề có logo

    Object.keys(worksheet).forEach(key => {
      // Bỏ qua các ô không phải dữ liệu (như !ref, !margins, v.v.)
      if (key[0] !== '!') {
        const cell = worksheet[key];
        // Nếu cell có giá trị và chứa "\n"
        if (cell && cell.v && typeof cell.v === 'string' && cell.v.includes('\n')) {
          // Thiết lập wrapText để cho phép xuống dòng
          if (!cell.s) cell.s = {};
          cell.s.alignment = { wrapText: true, vertical: 'center' };
        }
      }
    });
    
    // Tạo workbook và thêm worksheet
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Lịch sử nạp cước');
    
    // Xuất file Excel
    XLSX.writeFile(workbook, 'history_topup_mobile.xls');
    
    // Xóa phần tử tạm
    document.body.removeChild(tempDiv);
}
const formatDatePlaceholder = (dateString) => {
  if (!dateString) return '';
  const [year, month, day] = dateString.split('-');
  return `${day}/${month}/${year}`;
};
const handleDateChangeStart = (e) => {
  let newValue = e.target.value;
  formFilter.value.start_date = newValue;
  const wrapper = document.querySelector('.date-input-wrapper-start');
  if (wrapper) {
    wrapper.setAttribute('data-value', formatDatePlaceholder(newValue));
  }
  const wrapper_end = document.querySelector('.date-input-wrapper-end');
  if (wrapper_end) {
    wrapper_end.setAttribute('data-value', t('balance_history.end_date'));
  }
};
const handleDateChangeEnd = (e) => {
  let newValue = e.target.value;
  formFilter.value.end_date = newValue;
  const wrapper = document.querySelector('.date-input-wrapper-end');
  if (wrapper) {
    wrapper.setAttribute('data-value', formatDatePlaceholder(newValue));
  }
};
</script>
<style scoped>

.date-input-wrapper {
  position: relative;
  width: 100%;
}

.date-input {
  width: 100%;
  height: 40px;
  padding: 8px 12px;
  padding-right: 40px; /* Để tránh text đè lên icon */
  border: 1px solid #E5E7EB;
  border-radius: 6px;
  background-color: transparent;
  z-index: 1;
}

/* Style cho mobile */
@media (max-width: 768px) {
  .date-input {
    opacity: 0; /* Ẩn input gốc nhưng vẫn giữ chức năng */
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    -webkit-appearance: none;
  }

  /* Placeholder giả */
  .fake-placeholder {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #6B7280;
    pointer-events: none;
    z-index: 0;
  }

  /* Icon calendar giả */
  .calendar-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #6B7280;
    pointer-events: none;
    z-index: 0;
  }

  /* Container style */
  .date-input-wrapper {
    background: white;
    border: 1px solid #E5E7EB;
    border-radius: 6px;
    min-height: 40px;
    display: flex;
    align-items: center;
  }
}
.date-input-wrapper {
  position: relative;
  width: 100%;
  height: 40px;
  border: 1px solid #E5E7EB;
  border-radius: 6px;
  background: white;
}

.date-input {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 8px 12px;
  border: none; /* Bỏ border vì đã có ở wrapper */
  background: transparent;
  z-index: 2; /* Đảm bảo input luôn ở trên cùng để nhận click */
  opacity: 0; /* Làm trong suốt nhưng vẫn nhận được sự kiện */
  cursor: pointer;
}

.fake-placeholder {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #6B7280;
  pointer-events: none; /* Không can thiệp vào sự kiện click */
  z-index: 1;
}

.calendar-icon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #6B7280;
  pointer-events: none;
  z-index: 1;
}

/* Hiển thị giá trị đã chọn */
.date-input-wrapper::after {
  content: attr(data-value);
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
  pointer-events: none;
}
</style>