{"comment": {"title": "Evaluate", "write_comment_description": "Write your review", "write_comment_placeholder": "Share your opinion (Max 500 characters)", "submit": "Comment", "delete": "Delete review", "confirm_delete_title": "Confirm deletion", "confirm_delete_content": "Are you sure you want to delete this review?", "cancel": "Cancel", "ok": "Agree", "score": "Point", "comment_at": "Comments at", "delete_success": "Comment has been deleted", "delete_error": "This comment cannot be deleted. Please try again later.", "created_successfully": "Comments have been posted.", "created_error": "Cannot post comments"}, "product_order": {"card_title": "Telephone card", "card_description": "  * The shop has Google play Korea, USA cards. The card can be used to top up most games and applications running on Android operating systems such as Samsung, LG, Oppo... Contact admin to order this type of card.", "select_price": "Select amount", "selected_card": "You are selecting a card", "add_to_cart": "Add to cart", "cart_title": "Shopping Cart", "cart_empty": "Cart is empty", "cart_total": "Total", "cart_quantity": "Quantity", "apply_point": "Apply point offer", "points_applied": "Points applied", "select_point": "Select number of points", "total_payment": "Total payment", "payment": "Pay", "term_condition_1": "By selecting 'Checkout' you agree to the ", "term_condition_link": "Service provision, cancellation and refund policy", "featured_card": "Featured game cards", "buy": "Buy", "price": "Price", "selected_product": "You are selecting", "added_to_cart": "Added to cart", "invalid_point": "Invalid points", "login_to_apply_point": "Login to receive point offers", "discount": "Discount", "over_max_order": "The maximum quantity for the entire cart is {{maxQuantity}}", "view_order": "View Order", "points_used": "Points used"}, "deposit": {"create_deposit_request": "Create a deposit request", "deposit_amount": "Deposit amount", "payment_method": "Payment method", "constant_fee": "Transaction Fee", "min_amount": "Minimum transaction", "max_amount": "Maximum transaction", "min": "minimum", "max": "maximum", "total_amount": "Total amount", "note_1": " If you use someone else's credit card and are criminally prosecuted, you will be held fully responsible.", "note_2": "By clicking the button ", "note_2_1": "&quot;Pay&quot;", "note_2_2": " means you have read", "note_2_3": " our terms of service", "pay": "Pay", "history_title": "Deposit history", "transaction_id": "GD Code", "deposit_into_wallet": "Deposit to wallet", "amount": "Amount", "method": "Form", "created_date": "Date created", "status": "Status", "action": "Act", "view": "See", "default_fetch_message": "An error occurred while loading data.", "created_payment_request": "You have been redirected to the payment page in a few seconds.", "error_create_payment": "An error occurred while creating the payment request. Please try again later.", "payment_info": "Payment information"}, "orderStatus": {"PENDING": "Waiting", "PROCESSING": "Processing", "COMPLETED": "Complete", "CANCELED": "Canceled", "FAILED": "Failure", "EXPIRED": "Expired", "REFUNDED": "Refund", "CHARGEBACK": "Dispute", "UNPAID_CANCELED": "Canceled", "PAID_CANCELED": " Canceled &amp; Refunded"}, "paymentStatus": {"paid": "Paid", "unpaid": "Unpaid"}, "card_featured": {"title": "Featured scratch cards", "page_title": "List of scratch cards", "price_title": "Price", "buy_title": "Buy", "buy_button_title": "Pay"}, "topup_korean": {"topup_type": "Top-up Type", "regular_phone": "Voice Package (Regular)", "special_phone": "Voice Package (Special)", "data_package": "Data Package"}, "phone_top_up": {"prepay": "Prepay", "post_pay": "Pay later", "topup_korean": "Korean Top-up", "stt": "STT", "phone": "Phone number", "brand": "Network", "amount": "Amount", "pay_price": "Payment amount", "add_more": "More", "import_excel": "Import from excel", "select_brand": "Select carrier", "fill_all_fields": "Please fill in all information", "post_pay_cart_label": "Postpaid top-up", "prepay_cart_label": "Prepaid Top Up", "select_amount": "Select amount", "pre_pay_cart_label": "Prepaid Top Up", "import_excel_error": "Excel file is not in correct format", "phone_number": "Phone number", "phone_number_placeholder": "Enter phone number", "paylate": "Pay later", "topup_mobile": "Prepay", "please_enter_phone": "Please enter information before adding a new line"}, "bill": {"no_debt": "This bill has no outstanding charges or is not due yet.", "select_vendor": "Please select a provider"}, "top_up_game": {"game_account": "Account to be loaded", "game_account_placeholder": "Your game account ID", "game_account_hint": "The deposit amount will be added to this game account.", "top_up": "Top up game", "game_add_to_cart": "Add to cart", "game_description": "Add your notes", "game_description_placeholder": "Fill in server information or other notes if any"}, "withdraw": {"title": "Create a withdrawal request", "from_wallet": "Withdraw from wallet", "amount": "Amount to withdraw", "fee": "Fee Schedule", "total_amount": "Total amount", "receive_account": "Recipient account information", "register_account": "Update information", "register_account_hint": "Click “Update information” to update your bank account.", "withdraw": "Withdraw money", "bank_account_number": "Account number", "bank_name": "Bank", "bank_account_name": "Account holder", "confirm_withdraw": "Confirm withdrawal ", "confirm_withdraw_desc_1": "Confirm withdrawal to your bank. You cannot cancel this action. ", "confirm_withdraw_desc_2": "Note: Upon confirmation of withdrawal, the amount will be deducted from your wallet. The amount will be returned to the wallet if the transfer is not successful.", "confirm": "Confirm", "withdraw_success": "Withdrawn successfully ", "withdraw_success_des": "Withdrawal request has been submitted. The amount has been deducted from your wallet and will be returned to your wallet if the withdrawal is unsuccessful.", "min_amount": "Minimum withdrawal amount", "max_amount": "Maximum withdrawal amount", "amount_placeholder": "Enter the amount you want to withdraw", "error": "An error occurred while withdrawing funds.", "insufficient_balance": "Insufficient balance"}, "common": {"copied_to_clipboard": "Copied to clipboard", "copied_failed": "Co<PERSON> failed", "cancel": "Cancel", "confirm": "Confirm", "ok": "OK", "close": "Close", "delete": "Erase", "previous": "<", "next": "&gt;"}, "error_code": {"INSUFFICIENT_BALANCE": "Insufficient balance"}, "card": {"select_card_price": "Select card value"}, "toast_success_title": "Success", "toast_error_title": "Error", "checkout": {"insufficient_balance": "Insufficient funds in wallet. Please top up or choose another payment method.", "payment_success": "Payment successful", "should_enable_two_fa_title": "Recommended to enable 2-factor authentication! ????", "enable_two_fa_description": "Turn on two-factor authentication (2FA) today to increase the security of your account. This helps prevent unauthorized access even if your password is compromised.", "enable_two_fa": "Enable 2-factor authentication", "continue_pay": "Continue payment", "comback_to_cart": "Back", "enable_ekyc_description": "Purchase request failed because you have not verified eKYC.", "deposit_now": "<PERSON><PERSON><PERSON><PERSON>", "need_deposit_description": "Your purchase request was unsuccessful because you do not have enough funds in your account.", "enable_verify_phone_description": "Verify your phone number to make a purchase. This process doesn't take long.", "verify_now": "Verify now", "an_error_occurred": "An error occurred.", "verify_phone": "Verify phone number", "invalid_2fa": "Incorrect 2-factor password", "insufficient_balance_for_payment_method": "Your current balance is insufficient for this payment method.", "daily_transaction_limit_exceeded": "Daily transaction limit exceeded", "enter_2fa": "Enter 2-step password", "phone_verification_required": "Phone number verification required", "e_kyc_required": "e-KYC Authentication Required", "two_fa_enable": "Enable 2-factor authentication", "redirect_to_payment_soon": "You will be redirected to the payment page in a few seconds.", "payment_error": "The payment gateway is currently under maintenance. Please select another payment method.", "cart_empty": "Cart is empty", "order_detail": "Order information", "email_invalid": "Invalid email", "stt": "STT", "product_name": "Product Name", "price": "Price", "unit_price": "Unit price", "quantity": "SL", "total": "Total", "tong": "Total", "discount": "Discount", "fee": "Transaction Fee", "total_payment": "Total payment", "payment_info": "Payment information", "amount": "Amount", "payment_fee": "Transaction Fee", "point_discount": "Point offer", "total_pay": "Total amount", "email": "E-mail", "email_note": "*We will send the card code to this email address", "payment": "Pay", "two_factor_auth": "Two-factor authentication", "two_factor_auth_confirm": "Authentication", "error_occurred_try_again_later": "An error occurred, please try again later", "cannot_find_checkout_group": "Payment group not found", "please_enter_email": "Please enter email", "payment_gateway_maintenance_use_another": "Payment gateway is under maintenance, please use another payment gateway.", "redirecting_to_payment_page": "Redirecting to checkout page...", "invalid_otp": "Level 2 password is incorrect, please try again.", "TWO_FA_INVALID": "Invalid verification code", "payment_method": "Payment method"}, "two_fa_setting": {"verify_balance_payment": "Authentication when paying from wallet balance", "verify_card_history": "Verify when viewing card code history", "change_2fa_password": "Change level 2 password", "forgot_2fa_password": "Forgot level 2 password", "2fa_status": "2-factor authentication", "two_factor_auth": "Two-factor authentication", "enter_otp": "Set level 2 password", "enter_otp_hint": "Set the level 2 password as a 6-digit number string.", "enter_otp_confirm": "Reset Level 2 Password ", "enable_2fa_confirm": "Enable 2-factor authentication", "two_factor_auth_confirm": "Confirm", "otp_mismatch": "Level 2 passwords do not match. Please try again.", "otp": "Level 2 Password", "turn_off_2fa": "Turn off 2-factor authentication", "disable_2fa": "Turn off 2-factor authentication", "enable_success": "2-factor authentication enabled successfully.", "disable_success": "2-factor authentication is disabled", "wrong_2fa": "Wrong current level 2 password.", "enter_2fa": "Enter level 2 password", "change_2fa": "Set new level 2 password", "enter_old_pin": "Enter current level 2 password", "new_pin": "New Level 2 Password", "confirm_pin": "Confirm level 2 password", "old_pin_hint": "Enter old level 2 password", "change_success": "Change level 2 password successfully", "new_2fa_password": "Set new level 2 password", "verify_account": "Verify account", "forgot_success": "2-factor authentication update successful"}, "verify_otp": {"hint_email": "Please check your email: {{email}} to receive the OTP code.", "not_receive": "Haven't received OTP?", "resend": "Resend OTP", "confirm": "OTP Confirmation", "loading": "Loading...", "success_otp_sent": "OTP sent successfully", "error_send_otp": "OTP sending failed", "success_otp_verified": "OTP has been successfully confirmed", "error_verify_otp": "OTP code is incorrect or expired.", "error_invalid_otp": "Please enter valid OTP code"}, "orders_history": {"invoice_id": "Order code", "trans_code": "Transaction Code", "product": "Product", "order_status": "Order Status", "payment_status": "Payment Status", "ammount": "Amount", "created_date": "Created Date", "action": "Action", "all_status": "All Statuses", "search": "Search", "export_excel": "Export to Excel", "order_code": "Order Code", "tran_date": "Transaction Date", "order_history_card": "Top-up History", "order_history_topup_game": "Game Card Purchase Orders", "order_history_topup": "Top-up History", "order_history_bill": "Bill Payment History", "card_price": "Card Value"}, "balance_history": {"history_balance": "Wallet transaction history", "keyword": "GD code or content", "start_date": "Start date", "end_date": "End date", "pay_price": "Amount", "before_transaction": "Before the transaction", "after_transaction": "After the transaction", "currency": "Money", "note": "Note", "view_order": "Detail"}, "cart": {"cart_item_max_qty": "The number of products in the shopping cart cannot exceed {{maxQty}}", "recheck_cart_and_retry": "Please check your cart and try again.", "few_items_not_available": "Some items in your cart are no longer available.", "reset_cart": "Refresh cart", "cancel": "Cancel", "apply_point": {"ORDER_VALUE_POINT_CONVERSION": "The number of converted points exceeds the order value.", "NOT_ENOUGH_POINT": "Over points applied", "INVALID_POINT": "Invalid Point Number"}, "currency_mismatch_error": "Currency change detected. Please refresh your cart."}, "topup_mobile": {"upload_excel": "Upload Excel", "download_excel": "Download Excel Template", "in_excel": "in Excel", "topup_mobile_desc": "Notes for Excel template:<br>Column A; \"Số điện thoại\":<br>Only enter valid phone numbers, exactly 10 digits.<br>No spaces or special characters.<br>Column B: \"Nhà Mạng\":<br>Enter correct carrier name, example: Viettel, Vinaphone, Mobifone, Vietnamobile.<br>Column C: \"Số tiền nạp\":<br>For postpaid subscribers: Enter any amount (no special characters)."}, "import_excel": "Import Excel", "subscriber": "Subscriber", "points_used_amount": "Points used"}