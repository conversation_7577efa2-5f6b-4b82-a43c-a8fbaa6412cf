{"comment": {"title": "Đánh giá", "write_comment_description": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>h giá của bạn", "write_comment_placeholder": "Chia sẻ ý kiến của bạn (Tối đa 500 ký tự)", "submit": "<PERSON><PERSON><PERSON> lu<PERSON>", "delete": "<PERSON>óa đ<PERSON> giá", "confirm_delete_title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "confirm_delete_content": "Bạn có chắc chắn muốn xóa đánh giá này không?", "cancel": "<PERSON><PERSON><PERSON>", "ok": "Đồng ý", "score": "<PERSON><PERSON><PERSON><PERSON>", "comment_at": "<PERSON><PERSON><PERSON> luận tại", "delete_success": "<PERSON><PERSON><PERSON> luận đã đư<PERSON><PERSON> xóa", "delete_error": "<PERSON><PERSON><PERSON><PERSON> thể xóa bình luận này. <PERSON><PERSON> lòng thử lại sau", "created_successfully": "<PERSON><PERSON><PERSON> luận đã đư<PERSON>c đăng", "created_error": "<PERSON><PERSON><PERSON><PERSON> thể đăng bình luận"}, "product_order": {"card_title": "Thẻ điện thoại", "card_description": "  * Shop có các loại thẻ Google play Korea, USA. Thẻ dùng nạp được hầu hết các game, ứng dụng chạy\n          trên hệ điều\n          hành <PERSON> như <PERSON>, LG, Oppo...\n          <PERSON><PERSON><PERSON> hệ admin để được đặt hàng loại thẻ này.", "select_price": "<PERSON><PERSON><PERSON> số tiền", "selected_card": "Bạn đang chọn thẻ", "add_to_cart": "Thêm vào giỏ hàng", "cart_title": "Giỏ hàng", "cart_empty": "Giỏ hàng trống", "cart_total": "<PERSON><PERSON><PERSON> cộng", "cart_quantity": "Số lượng", "apply_point": "<PERSON><PERSON> dụng <PERSON>u đãi point", "points_applied": "Số point đ<PERSON><PERSON><PERSON> dụng", "select_point": "<PERSON><PERSON><PERSON> số point", "total_payment": "<PERSON><PERSON><PERSON> tiền thanh toán", "payment": "<PERSON><PERSON> toán", "term_condition_1": "Bằng việc chọn '<PERSON><PERSON> toán', bạn đồng ý với ", "term_condition_link": "<PERSON><PERSON><PERSON> s<PERSON>ch cung cấp, h<PERSON><PERSON> và hoàn tr<PERSON> dịch vụ", "featured_card": "Thẻ game n<PERSON>i bật", "buy": "<PERSON><PERSON>", "price": "Giá", "selected_product": "Bạn đang chọn", "added_to_cart": "<PERSON><PERSON> thêm vào giỏ hàng", "invalid_point": "Số point kh<PERSON>ng hợp lệ", "login_to_apply_point": "<PERSON><PERSON><PERSON> nhập để nhận ưu đãi point", "discount": "<PERSON><PERSON><PERSON>", "over_max_order": "<PERSON><PERSON> lượng tối đa cho toàn bộ giỏ hàng là {{maxQuantity}}", "view_order": "<PERSON><PERSON>", "points_used": "Số point sử dụng"}, "deposit": {"create_deposit_request": "<PERSON><PERSON><PERSON> y<PERSON>u c<PERSON>u nạp tiền", "deposit_amount": "<PERSON><PERSON> tiền n<PERSON>p", "payment_method": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "constant_fee": "<PERSON><PERSON> giao d<PERSON>ch", "min_amount": "<PERSON><PERSON><PERSON> d<PERSON>ch tối thiểu", "max_amount": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> tối đa", "min": "t<PERSON>i thi<PERSON>u", "max": "tối đa", "total_amount": "<PERSON><PERSON><PERSON> tiền", "note_1": " <PERSON><PERSON><PERSON> như bạn sử dụng thẻ tín dụng của người khác và bị tố cáo hình sự thì bạn phải chịu tất cả trách nhiệm.", "note_2": "Bằng việc nhấn vào nút ", "note_2_1": "\"Thanh toán\"", "note_2_2": " đồng ngh<PERSON>a bạn đã đọc", "note_2_3": " <PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON> dịch vụ của chúng tôi", "pay": "<PERSON><PERSON> toán", "history_title": "<PERSON><PERSON><PERSON> sử nạp tiền", "transaction_id": "Mã GD", "deposit_into_wallet": "Nạp vào ví", "amount": "<PERSON><PERSON> tiền", "method": "<PERSON><PERSON><PERSON>", "created_date": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "action": "<PERSON><PERSON><PERSON> đ<PERSON>", "view": "Xem", "default_fetch_message": "<PERSON><PERSON> lỗi xảy ra khi tải dữ liệu", "created_payment_request": "Bạn đã đư<PERSON><PERSON> chuyển hướng tới trang thanh toán trong vài giây.", "error_create_payment": "<PERSON><PERSON> lỗi xảy ra khi tạo yêu cầu thanh toán. <PERSON><PERSON> lòng thử lại sau.", "payment_info": "Thông tin thanh toán"}, "orderStatus": {"PENDING": "<PERSON><PERSON> chờ", "PROCESSING": "<PERSON><PERSON> lý", "COMPLETED": "<PERSON><PERSON><PERSON> th<PERSON>", "CANCELED": "<PERSON><PERSON> hủy", "FAILED": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "EXPIRED": "<PERSON><PERSON><PERSON>", "REFUNDED": "<PERSON><PERSON><PERSON> ti<PERSON>n", "CHARGEBACK": "<PERSON><PERSON><PERSON> ch<PERSON>p", "UNPAID_CANCELED": "<PERSON><PERSON> hủy", "PAID_CANCELED": " <PERSON><PERSON> h<PERSON> & <PERSON><PERSON><PERSON> tiền"}, "paymentStatus": {"paid": "<PERSON><PERSON> thanh toán", "unpaid": "<PERSON><PERSON><PERSON> to<PERSON>"}, "card_featured": {"title": "Thẻ cào nổi bật", "page_title": "<PERSON><PERSON> sách thẻ cào", "price_title": "Giá", "buy_title": "<PERSON><PERSON>", "buy_button_title": "<PERSON><PERSON> toán"}, "topup_korean": {"topup_type": "<PERSON><PERSON><PERSON> hình nạp tiền", "regular_phone": "<PERSON><PERSON><PERSON> (thông thườ<PERSON>)", "special_phone": "<PERSON><PERSON><PERSON> (đặc biệt)", "data_package": "Gói data"}, "phone_top_up": {"prepay": "<PERSON><PERSON><PERSON> trước", "post_pay": "Trả sau", "topup_korean": "<PERSON><PERSON>p thẻ <PERSON>àn <PERSON>c", "stt": "STT", "phone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "brand": "<PERSON><PERSON><PERSON> mạng", "amount": "<PERSON><PERSON> tiền", "pay_price": "<PERSON><PERSON> tiền thanh toán", "add_more": "<PERSON><PERSON><PERSON><PERSON>", "import_excel": "<PERSON><PERSON><PERSON><PERSON> từ excel", "select_brand": "<PERSON><PERSON><PERSON> nhà mạng", "fill_all_fields": "<PERSON><PERSON> lòng điền đầy đủ thông tin", "post_pay_cart_label": "<PERSON><PERSON><PERSON> tiền trả sau", "prepay_cart_label": "<PERSON><PERSON><PERSON> tiền trả trước", "select_amount": "<PERSON><PERSON><PERSON> số tiền", "pre_pay_cart_label": "<PERSON><PERSON><PERSON> tiền trả trước", "import_excel_error": "File excel không đúng định dạng", "phone_number": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "phone_number_placeholder": "<PERSON><PERSON><PERSON><PERSON> số điện thoại", "paylate": "Trả sau", "topup_mobile": "<PERSON><PERSON><PERSON> trước", "please_enter_phone": "<PERSON>n hãy điền thông tin trước khi thêm dòng mới"}, "bill": {"no_debt": "<PERSON><PERSON>a đơn này không có nợ cước hoặc chưa tới kì thanh toán", "select_vendor": "<PERSON><PERSON> lòng chọn nhà cung cấp"}, "top_up_game": {"game_account": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> n<PERSON>p", "game_account_placeholder": "ID tài khoản game của bạn", "game_account_hint": "<PERSON><PERSON> tiền nạp sẽ được cộng vào tài khoản game  này.", "top_up": "<PERSON>ạp tiền vào game", "game_add_to_cart": "Thêm vào giỏ hàng", "game_description": "<PERSON><PERSON><PERSON><PERSON> ghi chú của bạn", "game_description_placeholder": "Điền thông tin máy chủ hoặc ghi chú khác nếu có"}, "withdraw": {"title": "<PERSON><PERSON><PERSON> y<PERSON>u c<PERSON>u rút tiền", "from_wallet": "<PERSON><PERSON><PERSON> từ ví", "amount": "Số tiền muốn rút", "fee": "<PERSON><PERSON><PERSON><PERSON> phí", "total_amount": "<PERSON><PERSON><PERSON> tiền", "receive_account": "Thông tin tài khoản nhận tiền", "register_account": "<PERSON><PERSON><PERSON> nhật thông tin", "register_account_hint": "Ấn “<PERSON>ậ<PERSON> nhật thông tin” cập nhật lại tài khoản ngân hàng của bạn.", "withdraw": "<PERSON><PERSON><PERSON>", "bank_account_number": "Số tài <PERSON>n", "bank_name": "<PERSON><PERSON> h<PERSON>", "bank_account_name": "<PERSON><PERSON> tà<PERSON>n", "confirm_withdraw": "<PERSON><PERSON><PERSON> ", "confirm_withdraw_desc_1": "<PERSON><PERSON><PERSON> nhận rút tiền về ngân hàng của bạn. Bạn không thể hủy hành động này. ", "confirm_withdraw_desc_2": "Lưu ý: <PERSON><PERSON> x<PERSON><PERSON> nhận rút tiền, số tiền sẽ được trừ vào ví của bạn. Số tiền sẽ được hoàn về ví nếu giao dịch chuyển tiền không được thực hiện thành công.", "confirm": "<PERSON><PERSON><PERSON>", "withdraw_success": "<PERSON><PERSON> rút thành công ", "withdraw_success_des": "<PERSON><PERSON><PERSON> cầu rút tiền đã được gửi. Số tiền đã trừ vào ví của bạn và sẽ được hoàn về ví nếu rút tiền không thành công.", "min_amount": "<PERSON><PERSON> tiền rút tối thiểu", "max_amount": "S<PERSON> tiền rút tối đa", "amount_placeholder": "<PERSON><PERSON><PERSON><PERSON> số tiền muốn rút", "error": "<PERSON><PERSON> lỗi xảy ra khi rút tiền", "insufficient_balance": "Số dư không đủ"}, "common": {"copied_to_clipboard": "Đã sao chép vào clipboard", "copied_failed": "<PERSON><PERSON> ch<PERSON>p thất b<PERSON>i", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON>", "ok": "OK", "close": "Đ<PERSON><PERSON>", "delete": "Xóa", "previous": "<", "next": ">"}, "error_code": {"INSUFFICIENT_BALANCE": "Số dư không đủ"}, "card": {"select_card_price": "<PERSON><PERSON><PERSON> mệnh giá thẻ"}, "toast_success_title": "<PERSON><PERSON><PERSON><PERSON> công", "toast_error_title": "Lỗi", "checkout": {"insufficient_balance": "Số tiền trong ví không đủ. <PERSON>ui lòng nạp tiền hoặc chọn phương thức thanh to<PERSON> kh<PERSON>.", "payment_success": "<PERSON><PERSON> to<PERSON> thành công", "should_enable_two_fa_title": "<PERSON><PERSON><PERSON><PERSON><PERSON> nghị bật xác thực 2 lớp! 🔐", "enable_two_fa_description": "<PERSON><PERSON>t x<PERSON><PERSON> thực hai lớp (2FA) ngay hôm nay để tăng cường bảo mật cho tài khoản của bạn. <PERSON><PERSON><PERSON>u này giúp ngăn chặn truy cập trái phép ngay cả khi mật khẩu bị lộ.", "enable_two_fa": "<PERSON>ật x<PERSON>c thực 2 lớp", "continue_pay": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h toán", "comback_to_cart": "Quay về", "enable_ekyc_description": "<PERSON><PERSON><PERSON> cầu mua hàng không thành công do bạn chưa xác thực eKYC.", "deposit_now": "<PERSON><PERSON><PERSON> t<PERSON>", "need_deposit_description": "<PERSON><PERSON><PERSON> cầu mua hàng không thành công do bạn không đủ tiền trong tài khoản.", "enable_verify_phone_description": "<PERSON><PERSON><PERSON> thực số điện thoại để mua hàng. Qu<PERSON> trình này không mất nhiều thời gian.", "verify_now": "<PERSON><PERSON><PERSON> th<PERSON>c ngay", "an_error_occurred": "<PERSON><PERSON> lỗi xảy ra", "verify_phone": "<PERSON><PERSON><PERSON> thực số điện thoại", "invalid_2fa": "Mật khẩu 2 lớp không ch<PERSON>h xác", "insufficient_balance_for_payment_method": "<PERSON><PERSON> dư hiện tại không đủ cho ph<PERSON><PERSON><PERSON> thức thanh to<PERSON> n<PERSON>.", "daily_transaction_limit_exceeded": "<PERSON><PERSON><PERSON><PERSON> quá hạn mức giao dịch trong ngày", "enter_2fa": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>u 2 lớp", "phone_verification_required": "<PERSON><PERSON><PERSON> c<PERSON>u xác thực số điện thoại", "e_kyc_required": "<PERSON><PERSON><PERSON> c<PERSON>u xác thực e-KYC", "two_fa_enable": "<PERSON>ật x<PERSON>c thực 2 lớp", "redirect_to_payment_soon": "Bạn sẽ được chuyển hướng tới trang thanh toán trong vài giây.", "payment_error": "<PERSON><PERSON><PERSON> thanh toán hiện tại đang bảo trì. <PERSON><PERSON> lòng chọn phư<PERSON><PERSON> thức thanh toán k<PERSON>.", "cart_empty": "Giỏ hàng trống", "order_detail": "<PERSON><PERSON><PERSON><PERSON> tin đơn hàng", "email_invalid": "<PERSON><PERSON> h<PERSON> l<PERSON>", "stt": "STT", "product_name": "<PERSON><PERSON><PERSON> s<PERSON>n p<PERSON>m", "price": "Giá", "unit_price": "Đơn giá", "quantity": "SL", "total": "<PERSON><PERSON><PERSON> cộng", "tong": "Tổng", "discount": "<PERSON><PERSON><PERSON>", "fee": "<PERSON><PERSON> giao d<PERSON>ch", "total_payment": "<PERSON><PERSON><PERSON> tiền thanh toán", "payment_info": "Thông tin thanh toán", "amount": "<PERSON><PERSON> tiền", "payment_fee": "<PERSON><PERSON> giao d<PERSON>ch", "point_discount": "Ưu đãi point", "total_pay": "<PERSON><PERSON><PERSON><PERSON> tiền", "email": "Email", "email_note": "*Chúng tôi sẽ gửi mã thẻ qua địa chỉ email này", "payment": "<PERSON><PERSON> toán", "two_factor_auth": "<PERSON><PERSON><PERSON> thực hai lớp", "two_factor_auth_confirm": "<PERSON><PERSON><PERSON> th<PERSON>c", "error_occurred_try_again_later": "<PERSON><PERSON> lỗi x<PERSON> ra, vui lòng thử lại sau", "cannot_find_checkout_group": "<PERSON><PERSON><PERSON><PERSON> thể tìm thấy nhóm thanh toán", "please_enter_email": "<PERSON><PERSON> lòng nhập email", "payment_gateway_maintenance_use_another": "<PERSON><PERSON>ng thanh toán đang bảo trì, vui lòng sử dụng cổng thanh toán k<PERSON>.", "redirecting_to_payment_page": "<PERSON><PERSON> chuyển hướng tới trang thanh toán...", "invalid_otp": "<PERSON><PERSON><PERSON> kh<PERSON>u cấp 2 kh<PERSON><PERSON> ch<PERSON>, vui lòng thử lại.", "TWO_FA_INVALID": "<PERSON><PERSON> xác thực không hợp lệ", "payment_method": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán"}, "two_fa_setting": {"verify_balance_payment": "<PERSON><PERSON><PERSON> thực khi thanh toán từ số dư ví", "verify_card_history": "<PERSON><PERSON><PERSON> thực khi xem lịch sử mã thẻ", "change_2fa_password": "<PERSON><PERSON><PERSON> mật kh<PERSON> cấp 2", "forgot_2fa_password": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>u cấp 2", "2fa_status": "<PERSON>ác thực 2 lớp", "two_factor_auth": "<PERSON><PERSON><PERSON> thực hai lớp", "enter_otp": "Đặt mật kh<PERSON>u cấp 2", "enter_otp_hint": "Đặt mật kh<PERSON>u cấp 2 là dãy số có 6 chữ số.", "enter_otp_confirm": "Đặt lại mật kh<PERSON>u cấp 2 ", "enable_2fa_confirm": "<PERSON>ật x<PERSON>c thực 2 lớp", "two_factor_auth_confirm": "<PERSON><PERSON><PERSON>", "otp_mismatch": "<PERSON><PERSON><PERSON> kh<PERSON>u cấp 2 không khớp. <PERSON><PERSON> lòng thử lại.", "otp": "<PERSON><PERSON><PERSON> cấp 2", "turn_off_2fa": "Tắt x<PERSON>c thực 2 lớp", "disable_2fa": "Tắt x<PERSON>c thực 2 lớp", "enable_success": "<PERSON><PERSON><PERSON> x<PERSON>c thực 2 lớp thành công.", "disable_success": "<PERSON><PERSON><PERSON> thực 2 lớp đã đ<PERSON><PERSON><PERSON> tắt", "wrong_2fa": "<PERSON> mật kh<PERSON>u cấp 2 hiện tại.", "enter_2fa": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>u cấp 2", "change_2fa": "Đặt mật khấu cấp 2 mới", "enter_old_pin": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>u cấp 2 hiện tại", "new_pin": "<PERSON><PERSON><PERSON> cấp 2 mới", "confirm_pin": "<PERSON><PERSON><PERSON>n mật kh<PERSON>u cấp 2", "old_pin_hint": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>u cấp 2 cũ", "change_success": "<PERSON><PERSON><PERSON> mật kh<PERSON>u cấp 2 thành công", "new_2fa_password": "Đặt mật kh<PERSON>u cấp 2 mới", "verify_account": "<PERSON><PERSON><PERSON> thực tà<PERSON>n", "forgot_success": "<PERSON><PERSON><PERSON> nhập xác thực 2 lớp thành công"}, "verify_otp": {"hint_email": "<PERSON><PERSON> lòng kiểm tra email của bạn: {{email}} để nhận mã OTP.", "not_receive": "<PERSON><PERSON>a nhận đ<PERSON><PERSON>?", "resend": "Gửi lại OTP", "confirm": "<PERSON>ác nhận OTP", "loading": "<PERSON><PERSON> tả<PERSON>...", "success_otp_sent": "OTP đã đ<PERSON><PERSON><PERSON> gửi thành công", "error_send_otp": "Gửi OTP không thành công", "success_otp_verified": "OTP đã đư<PERSON><PERSON> xác nhận thành công", "error_verify_otp": "Mã OTP sai hoặc đã hết hạn.", "error_invalid_otp": "<PERSON><PERSON> lòng nh<PERSON>p mã O<PERSON> hợp lệ"}, "orders_history": {"invoice_id": "<PERSON><PERSON> đơn hàng", "trans_code": "Mã giao d<PERSON>ch", "product": "<PERSON><PERSON><PERSON> p<PERSON>m", "order_status": "TT Đơn hàng", "payment_status": "TT Thanh toán", "ammount": "<PERSON><PERSON> tiền", "created_date": "<PERSON><PERSON><PERSON>", "action": "<PERSON><PERSON><PERSON> đ<PERSON>", "all_status": "<PERSON><PERSON><PERSON> cả trạng thái", "search": "<PERSON><PERSON><PERSON>", "export_excel": "Xuất Excel", "order_code": "<PERSON><PERSON> đơn hàng", "tran_date": "Ngày GD", "order_history_card": "<PERSON><PERSON><PERSON> s<PERSON> n<PERSON> c<PERSON>", "order_history_topup_game": "Đơn hàng mua mã thẻ", "order_history_topup": "<PERSON><PERSON><PERSON> s<PERSON> nạp c<PERSON>c", "order_history_bill": "<PERSON><PERSON><PERSON> sử thanh to<PERSON> hóa đơn", "card_price": "Mệnh giá"}, "balance_history": {"history_balance": "<PERSON><PERSON><PERSON> sử giao dịch ví", "keyword": "Mã GD hoặc nội dung", "start_date": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u", "end_date": "<PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c", "pay_price": "<PERSON><PERSON> tiền", "before_transaction": "<PERSON><PERSON><PERSON><PERSON><PERSON> giao d<PERSON>ch", "after_transaction": "<PERSON>u giao d<PERSON>ch", "currency": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "note": "<PERSON><PERSON><PERSON>", "view_order": "<PERSON> ti<PERSON>"}, "cart": {"cart_item_max_qty": "<PERSON><PERSON> lượng sản phẩm trong giỏ hàng không được vư<PERSON><PERSON> quá {{maxQty}}", "recheck_cart_and_retry": "<PERSON><PERSON> lòng kiểm tra lại giỏ hàng và thử lại", "few_items_not_available": "Một số sản phẩm trong giỏ hàng không còn sẵn", "reset_cart": "<PERSON><PERSON><PERSON> mới giỏ hàng", "cancel": "<PERSON><PERSON><PERSON>", "apply_point": {"ORDER_VALUE_POINT_CONVERSION": "Số point quy đổi quá giá trị đơn hàng.", "NOT_ENOUGH_POINT": "<PERSON>uá số point đ<PERSON><PERSON><PERSON> dụng", "INVALID_POINT": "Số Point không hợp lệ"}, "currency_mismatch_error": "<PERSON><PERSON> phát hiện sự thay đổi về tiền tệ. <PERSON>ui lòng làm mới giỏ hàng."}, "topup_mobile": {"upload_excel": "<PERSON><PERSON><PERSON> excel lên", "download_excel": "Tải mẫu Excel", "in_excel": "trong excel", "topup_mobile_desc": "<PERSON><PERSON> chú cho mẫu Excel:<br><PERSON><PERSON><PERSON> A; \"Số điện thoại\":<br>Chỉ nhập các số điện thoại hợp lệ, đủ 10 chữ số.<br><PERSON>hông chứa khoảng trắng hoặc ký tự đặc biệt.<br>Cột B: \"Nhà mạng\":<br>Điền đúng tên nhà mạng, ví dụ: Viettel, Vinaphone, Mobifone, Vietnamobile.<br>Cột C: \"Số tiền nạp\":<br>Đối với thuê bao trả sau: Điền số tiền tùy ý (không có ký tự đặc biệt)."}, "import_excel": "Nhập Excel", "subscriber": "<PERSON><PERSON><PERSON> bao", "points_used_amount": "Số point đ<PERSON><PERSON><PERSON> sử dụng", "select_currency": "<PERSON><PERSON><PERSON> gi<PERSON> bán bạn muốn hiển thị", "setting_currency": "Cài đặt mua hàng", "setting_currency_description": "<PERSON><PERSON><PERSON> không thể thay đổi đơn vị tiền tệ khi đã tạo tài khoản."}