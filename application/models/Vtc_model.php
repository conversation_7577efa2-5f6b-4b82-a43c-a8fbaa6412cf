<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Vtc_model extends CI_Model {
   
	private $partnerCode;
    private $secretKey;
	private $host;
    private $privateKeyPath;

    public function __construct() {
        parent::__construct();
        $this->config->load('vtc'); // Load file cấu hình 
       
        // Get settings from extension table using Query Builder
        $extension = $this->db
            ->select('setting')
            ->from('extension')
            ->where('id', 4)
            ->where('status', 1)
            ->limit(1)
            ->get()
            ->row();

        if ($extension && $extension->setting) {
            $settings = json_decode($extension->setting, true);
            
            $this->partnerCode = isset($settings['partner_code']) ? $settings['partner_code'] : '';
            $this->secretKey = isset($settings['key_decode']) ? $settings['key_decode'] : '';
            $this->host = $this->config->item('host');
        }

        // Set private key based on environment
        $environment = getenv('CI_ENV');
       
        $keyFileName = ($environment === 'development') ? 'vtc_sb_merchant_privateKey.pem' : 'vtc_prod_privateKey.pem';
        $this->privateKeyPath = APPPATH . 'keys/' . $keyFileName;
        $CI =& get_instance();
        $CI->db->insert('log_activity', array(
            'logger_key' => 'VTC_$environment--'.$environment,
            'action' => '$host---'.getenv('VTC_HOST'),
            'key' => '$keyFileName---'.$keyFileName,
            'created' => now()
        ));
    }


    public function generateSignature($partnerCode, $categoryID, $productID, $productAmount, $customerID, $partnerTransID, $partnerTransDate, $data) {
        // Ghép dữ liệu theo format
        $stringToSign = implode('|', [
            $partnerCode,
            $categoryID,
            $productID,
            $productAmount,
            $customerID,
            $partnerTransID,
            $partnerTransDate,
            $data
        ]);
        // Đọc Private Key
        $privateKey = file_get_contents($this->privateKeyPath);
        $keyResource = openssl_pkey_get_private($privateKey);
     
        if (!$keyResource) {
            log_message('error', 'Không thể đọc private key');
            return false;
        }

        // Ký dữ liệu bằng RSA SHA256
        openssl_sign($stringToSign, $signature, $keyResource, OPENSSL_ALGO_SHA256);

        // Chuyển chữ ký sang Base64 để dễ truyền đi
        return base64_encode($signature);
    }

	public function verifySignature($stringToVerify, $signature) {
        // Đọc Public Key
        $publicKey = file_get_contents($this->publicKeyPath);
        $keyResource = openssl_pkey_get_public($publicKey);

        if (!$keyResource) {
            log_message('error', 'Không thể đọc public key');
            return false;
        }

        // Giải mã Base64 chữ ký
        $signature = base64_decode($signature);

        // Kiểm tra chữ ký
        return openssl_verify($stringToVerify, $signature, $keyResource, OPENSSL_ALGO_SHA256) === 1;
    }

    public function api_curl($postData, $router = '') {
		$postData['dataSign'] = $this->generateSignature($postData['partnerCode'], $postData['categoryID'], $postData['productID'], $postData['productAmount'], $postData['customerID'], $postData['partnerTransID'], $postData['partnerTransDate'], $postData['data']);
		$url = $this->host.$router;
        $ch = curl_init($url);

        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($postData));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            "Content-Type: application/json"
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        if ($httpCode == 200) {
					$listData = json_decode($response, true);
					return $listData;
        } else {
          return null;
        }

        return null;
    }

	public function get_all_categories() {
		
		 $postData = [
            "partnerCode" => $this->partnerCode,
            "categoryID" => "0",
            "productID" => "",
            "productAmount" => "",
            "customerID" => "",
            "partnerTransID" => "20250318221050654",
            "partnerTransDate" => date("YmdHis"),
            "data" => "",
            "otherInfo" => '',
            "dataSign" =>"",
        ];

		return $this->api_curl($postData, 'share/GetInfo/get-all-category');
	}

	public function get_categories($categoryID = 0) {
		if($categoryID) {
			$postData = [
				"partnerCode" => $this->partnerCode,
				"categoryID" => (string) $categoryID,
				"productID" => "",
				"productAmount" => "",
				"customerID" => "",
				"partnerTransID" => date("YmdHis"),
				"partnerTransDate" => date("YmdHis"),
				"data" => "",
				"otherInfo" => '',
				"dataSign" =>"",
			];
			
			return $this->api_curl($postData, 'share/GetInfo/get-category');
		}
		return false;
   }

    public function get_product($categoryID = 0) {
		if($categoryID) {
			$postData = [
				"partnerCode" => $this->partnerCode,
				"categoryID" => (string) $categoryID,
				"productID" => "",
				"productAmount" => "",
				"customerID" => "",
				"partnerTransID" => date("YmdHis"),
				"partnerTransDate" => date("YmdHis"),
				"data" => "",
				"otherInfo" => '',
				"dataSign" =>"",
			];
			
			return $this->api_curl($postData, 'share/GetInfo/get-product');
		}
		return false;
	}

    public function get_balance($test = false) {
        $postData = [
            "partnerCode" => $this->partnerCode,
            "categoryID" => "0", // Sử dụng "0" thay vì biến $categoryID không được định nghĩa
            "productID" => "",
            "productAmount" => "",
            "customerID" => "",
            "partnerTransID" => date("YmdHis"),
            "partnerTransDate" => date("YmdHis"),
            "data" => "",
            "otherInfo" => '',
            "dataSign" => "",
        ];
        
        $result = $this->api_curl($postData, 'share/GetInfo/get-balance');
        
        if ($result && isset($result['status']) && $result['status'] == 1) {
            if($test) {
                return isset($result['balance']) ? $result['balance'] : 1;
            } else {
                return isset($result['balance']) ? $result['balance'] : 0;
            }
            
        }
        
        return 0; // Trả về 0 nếu có lỗi
    }

	 /**
     * Mua ma the
     *
     * @param string $request_id
     * @param string $serviceCode
     * @param float $amount
     * @param int $quantity
     * @return array
     */
	public function buyCard($productCode, $quantity){
		try {
			$product = $this->db->where('product_code', $productCode)->get('vtc_product')->row();
		
			if($product) {
				$body = [
					"partnerCode" => (string)$this->partnerCode,
					"categoryID" => (string) $product->sub_category_id,
					"productID" => (string) $product->product_id,
					"productAmount" => (string) $product->value,
					"customerID" => "",
					"partnerTransID" => date("YmdHis"),
					"partnerTransDate" => date("YmdHis"),
					"data" =>  (string) $quantity,
					"otherInfo" => null,
					"dataSign" => "",
				];
			
				$data  = $this->api_curl($body, 'share/Pay/buy-card');
				if($data && $data['status'] == 1) {
					$dataInfo = $data['dataInfo'];
					$convertData = $this->decryptWith3DES($dataInfo);
					// To do Cập nhật data json vào table order
					return [
						'listCard' => $convertData['listcard'],
						'data' => $data,
					];
				} else {
					return [
						'listCard' => [],
						'data' => $data,
					];
				}
			}
			return null;
		} catch (\Throwable $th) {
			return null;
		}
		 
	}

	/**
	 * Nạp điện thoại trả trước
	 */
	public function topupMobile($request) {
        $customerID = $request->account;
        $productCode = $request->key_connection;

		$CI =& get_instance();

        $vtc_product = $this->db->where('product_code', $productCode)->get('vtc_product')->row();
        if($vtc_product) {
            $body = [
                "partnerCode" => (string)$this->partnerCode,
                "categoryID" => (string) $vtc_product->sub_category_id,
                "productID" => (string) $vtc_product->product_id,
                "productAmount" => (string) $vtc_product->value,
                "customerID" => $customerID,
                "partnerTransID" => date("YmdHis"),
                "partnerTransDate" => date("YmdHis"),
                "data" =>  '1',
                "otherInfo" => null,
                "dataSign" => "",
            ];
            
            $data  = $this->api_curl($body, 'share/Pay/topup-mobile');
            
            if($data && $data['status'] == 1) {
                $dataInfo = $data['dataInfo'];
                $convertData = $this->base64_decode($dataInfo);
    
                $CI->db->where('request_id', $request->request_id)
                ->update('log_product_provider_request', [
                    'provider_tran_id' => $data['partnerTransID'],
                    'balance'          => $data['balance'],
                    'completed' => now(),
                    'status' =>1
                ]);
                // To do Cập nhật data json vào table order
                return [
                    'listCard' => $convertData,
                    'data' => $data,
                ];
            } else {
                $CI->db->where('request_id', $request->request_id)
                ->update('log_product_provider_request', [
                    'provider_tran_id' => $data['partnerTransID'],
                    'balance'          => $data['balance'],
                    'completed' => now(),
                    'status' => 0,
                    'output' => json_encode(['listCard' => $data]),
                    'error' => $data['description']
                ]);
            }
        }
		return null;
	}

    /**
     * Nạp điện thoại trả sau
     */
    public function topupMobilePost($request) {
        $customerID = $request->account;
        $productCode = $request->key_connection;

        $CI =& get_instance();
		$log_product_provider_request = $CI->db->where('request_id', $request->request_id)->get('log_product_provider_request')->row();
      
        $product_order = $CI->db->select('product_order.*')
		->from('product_order')
		->where('product_order.invoice_order_id', $log_product_provider_request->invoice_order_id)
		->get()
		->row();

        $amount = (int)$product_order->amount;

        $vtc_product = $this->db->where('product_code', $productCode)->get('vtc_product')->row();
        if($vtc_product) {
            $body = [
                "partnerCode" => (string)$this->partnerCode,
                "categoryID" => (string) $vtc_product->sub_category_id,
                "productID" => (string) $vtc_product->product_id,
                "productAmount" => (string)$amount,
                "customerID" => $customerID,
                "partnerTransID" => date("YmdHis"),
                "partnerTransDate" => date("YmdHis"),
                "data" =>  '1',
                "otherInfo" => null,
                "dataSign" => "",
            ];
    
            $data  = $this->api_curl($body, 'share/Pay/topup-mobile');
            if($data && $data['status'] == 1) {
                $dataInfo = $data['dataInfo'];
                $convertData = $this->base64_decode($dataInfo);
    
                $CI->db->where('request_id', $request->request_id)
                ->update('log_product_provider_request', [
                    'provider_tran_id' => $data['partnerTransID'],
                    'balance'          => $data['balance'],
                    'completed' => now(),
                    'status' =>1
                ]);
                // To do Cập nhật data json vào table order
                return [
                    'listCard' => $convertData,
                    'data' => $data,
                ];
            } else {
                $CI->db->where('request_id', $request->request_id)
                ->update('log_product_provider_request', [
                    'provider_tran_id' => $data['partnerTransID'],
                    'balance'          => $data['balance'],
                    'completed' => now(),
                    'status' => 0,
                    'output' => json_encode(['listCard' => $data]),
                    'error' => $data['description']
                ]);
            }
        }
		return null;
    }

    public function payBill($request) {
        $customerID = $request->account;
        $productCode = $request->key_connection;

        $CI =& get_instance();
		$log_product_provider_request = $CI->db->where('request_id', $request->request_id)->get('log_product_provider_request')->row();
      
        $product_order = $CI->db->select('product_order.*')
		->from('product_order')
		->where('product_order.invoice_order_id', $log_product_provider_request->invoice_order_id)
		->get()
		->row();

        $amount = (int)$product_order->amount;

        $vtc_product = $this->db->where('product_code', $productCode)->get('vtc_product')->row();
        if($vtc_product) {
            $body = [
                "partnerCode" => (string)$this->partnerCode,
                "categoryID" => (string) $vtc_product->sub_category_id,
                "productID" => (string) $vtc_product->product_id,
                "productAmount" => (string)$amount,
                "customerID" => $customerID,
                "partnerTransID" => date("YmdHis"),
                "partnerTransDate" => date("YmdHis"),
                "data" =>  '1',
                "otherInfo" => null,
                "dataSign" => "",
            ];
            $data  = $this->api_curl($body, 'share/Pay/pay-bill');
            
            if($data && $data['status'] == 1) {
                $dataInfo = $data['dataInfo'];
                $convertData = $this->base64_decode($dataInfo);
    
                $CI->db->where('request_id', $request->request_id)
                ->update('log_product_provider_request', [
                    'provider_tran_id' => $data['partnerTransID'],
                    'balance'          => $data['balance'],
                    'completed' => now(),
                    'status' =>1
                ]);
                // To do Cập nhật data json vào table order
                return [
                    'listCard' => $convertData,
                    'data' => $data,
                ];
            } else {
                $CI->db->where('request_id', $request->request_id)
                ->update('log_product_provider_request', [
                    'provider_tran_id' => $data['partnerTransID'],
                    'balance'          => $data['balance'],
                    'completed' => now(),
                    'status' => 0,
                    'output' => json_encode(['listCard' => $data]),
                    'error' => $data['description']
                ]);
            }
        }
		return null;
    }

   /**
	 * Nạp điện thoại trả trước
	 */
	public function topupMobile($request) {
        $customerID = $request->account;
        $productCode = $request->key_connection;

		$CI =& get_instance();

        $vtc_product = $this->db->where('product_code', $productCode)->get('vtc_product')->row();
        if($vtc_product) {
            $body = [
                "partnerCode" => (string)$this->partnerCode,
                "categoryID" => (string) $vtc_product->sub_category_id,
                "productID" => (string) $vtc_product->product_id,
                "productAmount" => (string) $vtc_product->value,
                "customerID" => $customerID,
                "partnerTransID" => date("YmdHis"),
                "partnerTransDate" => date("YmdHis"),
                "data" =>  '1',
                "otherInfo" => null,
                "dataSign" => "",
            ];
            
            $data  = $this->api_curl($body, 'share/Pay/topup-mobile');
            
            if($data && $data['status'] == 1) {
                $dataInfo = $data['dataInfo'];
                $convertData = $this->base64_decode($dataInfo);
    
                $CI->db->where('request_id', $request->request_id)
                ->update('log_product_provider_request', [
                    'provider_tran_id' => $data['partnerTransID'],
                    'balance'          => $data['balance'],
                    'completed' => now(),
                    'status' =>1
                ]);
                // To do Cập nhật data json vào table order
                return [
                    'listCard' => $convertData,
                    'data' => $data,
                ];
            } else {
                $CI->db->where('request_id', $request->request_id)
                ->update('log_product_provider_request', [
                    'provider_tran_id' => $data['partnerTransID'],
                    'balance'          => $data['balance'],
                    'completed' => now(),
                    'status' => 0,
                    'output' => json_encode(['listCard' => $data]),
                    'error' => $data['description']
                ]);
            }
        }
		return null;
	}

    /**
     * Nạp Game
     */
    public function topupGame($request) {
        try {
            $customerID = $request->account;
            $productCode = $request->key_connection;
           
            $CI =& get_instance();
            $log_product_provider_request = $CI->db->where('request_id', $request->request_id)->get('log_product_provider_request')->row();
          
            $product_order = $CI->db->select('product_order.*')
            ->from('product_order')
            ->where('product_order.invoice_order_id', $log_product_provider_request->invoice_order_id)
            ->get()
            ->row();
    
            $quantity = (int)$product_order->quantity;
           
            $product = $this->db->where('id', $product_order->product_id)->get('product')->row();
           
            $price_currency = json_decode($product->prices_currency, true);
           
         
            $amount = (int)$price_currency[8];
           
    
            $vtc_product = $this->db->where('product_code', $productCode)->get('vtc_product')->row();
         
            if($vtc_product) {
               
                $body = [
                    "partnerCode" => (string)$this->partnerCode,
                    "categoryID" => (string) $vtc_product->sub_category_id,
                    "productID" => (string) $vtc_product->product_id,
                    "productAmount" => (string) $amount,
                    "customerID" => $customerID,
                    "partnerTransID" => date("YmdHis"),
                    "partnerTransDate" => date("YmdHis"),
                    "data" =>  (string)$quantity,
                    "otherInfo" => null,
                    "dataSign" => "",
                ];
               
                $data  = $this->api_curl($body, 'share/Pay/topup-game');
                if($data && $data['status'] == 1) {
                    // $dataInfo = $data['dataInfo'];
                    $CI->db->where('request_id', $request->request_id)
                    ->update('log_product_provider_request', [
                        'provider_tran_id' => $data['partnerTransID'],
                        'balance'          => $data['balance'],
                        'completed' => now(),
                        'status' =>1
                    ]);
                    // To do Cập nhật data json vào table order
                    return [
                        'listCard' => $data,
                        'data' => $data,
                    ];
                } else {
                    $CI->db->where('request_id', $request->request_id)
                    ->update('log_product_provider_request', [
                        'provider_tran_id' => $data['partnerTransID'],
                        'balance'          => $data['balance'],
                        'completed' => now(),
                        'status' => 0,
                        'output' => json_encode(['listCard' => $data]),
                        'error' => $data['description']
                    ]);
                    return null;
                }
            } else {
                $CI->db->insert('log_activity', array(
                    'logger_key' => '26-11-M-6',
                    'action' => '',
                    'key' => '',
                    'owner_type' => '',
                    'owner_key' => '',
                    'context' => '',
                    'ip' => '0',
                    'user_agent' => '0',
                    'session_id' => '0',
                    'url' => '',
                    'created' => now()
                ));
            }
            
            return null;
        } catch (\Throwable $th) {
            $CI =& get_instance();

            $CI->db->insert('log_activity', array(
                'logger_key' => '26-11-M-2',
                'action' => '',
                'key' => '',
                'owner_type' => '',
                'owner_key' => '',
                'context' => '',
                'ip' => '0',
                'user_agent' => '0',
                'session_id' => '0',
                'url' => '',
                'created' => now()
            ));
            return null;
        }
       
    }

	
	public function check_bill_info ($postData) {
		$body = [
			"partnerCode" => $this->partnerCode,
			"categoryID" => (string) $postData->sub_category_id,
			"productID" => (string) $postData->product_id,
			"productAmount" => "",
			"customerID" => (string) $postData->customer_code,
			"partnerTransID" => date("YmdHis"),
			"partnerTransDate" => date("YmdHis"),
			"data" => "",
			"otherInfo" => '',
			"dataSign" =>"",
		];
		return $this->api_curl($body, 'share/GetInfo/check-bill-info');
	}

	public function decryptWith3DES($encryptedData){
        $secret = $this->secretKey;
        $key = substr(md5($secret), 0, 24);
        $text = base64_decode($encryptedData);
        $data = mcrypt_decrypt(MCRYPT_TRIPLEDES, $key, $text, MCRYPT_MODE_ECB, '12345678');
        $block = mcrypt_get_block_size('tripledes', 'ecb');
        $packing = ord($data{strlen($data) - 1});

        if ($packing && ($packing < $block)) {
            for ($P = strlen($data) - 1; $P >= strlen($data) - $packing; $P--) {
                if (ord($data{$P}) != $packing) {
                    $packing = 0;
                }
            }
        }

        $my_string = strtolower(substr($data, 0, strlen($data) - $packing));;
        return json_decode($my_string, true);
    }

    public function base64_decode($base64) {
        $json = base64_decode($base64);

        // Chuyển JSON thành object
        $data = json_decode($json);
        return $data;
    }

	// Mua 
	public function pay_topup_mobile($postData) {
		
		$body = [
			"partnerCode" => $this->partnerCode,
			"categoryID" => (string) $postData->sub_category_id,
			"productID" => (string) $postData->product_id,
			"productAmount" => (string) $postData->value,
			"customerID" => (string) $postData->phone,
			"partnerTransID" => date("YmdHis"),
			"partnerTransDate" => date("YmdHis"),
			"data" => "",
			"otherInfo" => '',
			"dataSign" =>"",
		];
		return $this->api_curl($body, 'share/Pay/topup-mobile');
	}

	// check bill

	public function get_carddata() {
		$body = [
			"partnerCode" => $this->partnerCode,
			"categoryID" => '56',
			"productID" => '23',
			"productAmount" => '10000',
			"customerID" => '0385569663',
			"partnerTransID" => '20250319204738',
			"partnerTransDate" => date("YmdHis"),
			"data" => "",
			"otherInfo" => '',
			"dataSign" =>"",
		];
		return $this->api_curl($body, 'share/GetInfo/get-carddata');
	}

	public function check_partner_order() {
		$body = [
			"partnerCode" => $this->partnerCode,
			"categoryID" => '56',
			"productID" => '23',
			"productAmount" => '10000',
			"customerID" => '0385524224',
			"partnerTransID" => '20250319211332',
			"partnerTransDate" => date("YmdHis"),
			"data" => "",
			"otherInfo" => '',
			"dataSign" =>"",
		];
		return $this->api_curl($body, 'share/GetInfo/check-partner-order');
	}

}
