<?php namespace App\Product\Provider\Vtc;

use App\Product\Library\Provider\BillRequest;
use App\Product\Library\Provider\BillResponse;
use App\Product\Library\Provider\BuyCardRequest;
use App\Product\Library\Provider\BuyCardResponse;
use App\Product\Library\Provider\Card;
use App\Product\Library\Provider\FindTranRequest;
use App\Product\Library\Provider\FindTranResponse;
use App\Product\Library\Provider\GetBalanceResponse;
use App\Product\Library\Provider\GetCardRequest;
use App\Product\Library\Provider\GetCardResponse;
use App\Product\Library\Provider\ProviderTranStatus;
use App\Product\Library\Provider\TestResponse;
use App\Product\Library\Provider\TopupGameRequest;
use App\Product\Library\Provider\TopupGameResponse;
use App\Product\Library\Provider\TopupMobilePostRequest;
use App\Product\Library\Provider\TopupMobilePostResponse;
use App\Product\Library\Provider\TopupMobileRequest;
use App\Product\Library\Provider\TopupMobileResponse;
use App\Product\Library\Provider\TopupKoreanRequest;
use App\Product\Library\Provider\TopupKoreanResponse;
use App\Product\Library\ProviderService;
use DateTime;
use VtcGoods;

require_once APPPATH . 'libraries/vtc/VtcGoods.php';

class Service extends ProviderService
{
    /**
     * Doi tuong api service
     *
     * @var VtcGoods
     */
    protected $api;


    /**
     * Lay cac dich vu ho tro
     *
     * @return array
     */
    public function getServices()
    {
        return ['card', 'topup_mobile', 'topup_mobile_post', 'topup_game', 'bill', 'topup_korean'];
    }

    /**
     * Test ket noi
     *
     * @return TestResponse
     */
    public function test()
    {
        //$res = $this->api()->getCard('VTC0027', '10000', '13932202');
        //pr($res);

        // $res = $this->api()->getBalance();

        // if (!$this->isApiResponseSuccess($res)) {
        //     return TestResponse::error($this->getApiResponseError($res), $res);
        // }

        // return TestResponse::success($res);
        $CI =& get_instance();
        $CI->load->model('Vtc_model');
        $response = $CI->Vtc_model->get_balance(true);
        if($response && count($response) > 0) {
            return TestResponse::success('Kết nối thành công');
        } else {
            return TestResponse::error('Kết nối không thành công');
        }
    }

    /**
     * Lay balance
     *
     * @return GetBalanceResponse
     */
    public function getBalance()
    {
        $res = $this->api()->getBalance();

        if (!$this->isApiResponseSuccess($res)) {
            return GetBalanceResponse::error($this->getApiResponseError($res));
        }

        $balance = array_get($res, 'PartnerBalance');

        return GetBalanceResponse::success($balance);
    }

    /**
     * Lay doi tuong api
     *
     * @return VtcGoods
     */
    protected function api()
    {
        if (is_null($this->api)) {
            $this->api = new VtcGoods([
                'partnerCode' => $this->setting('partner_code'),
                'keyDecode' => $this->setting('key_decode'),
            ]);
        }

        return $this->api;
    }

    /**
     * Kiem tra ket qua tra ve tu api co thanh cong hay khong
     *
     * @param array $response
     * @return bool
     */
    protected function isApiResponseSuccess(array $response)
    {
        return in_array($response['ResponseCode'], [1, -290]);
    }

    /**
     * Lay api response error
     *
     * @param array $response
     * @return string
     */
    protected function getApiResponseError(array $response)
    {
        return array_get($response, 'ResponseCode') . ': ' . array_get($response, 'ResponseMsg');
    }

    /**
     * Mua ma the
     *
     * @param BuyCardRequest $request
     * @return BuyCardResponse
     */
    public function buyCard(BuyCardRequest $request)
    {
//        list($provider, $amount) = $request->parseKeyConnection(2);
//
//        if (!$provider || !$amount) {
//            return BuyCardResponse::error('Param key_connection invalid');
//        }
//
//        $res = $this->api()->buyCard($request->request_id, $provider, $amount, $request->quantity);
//
//        if (!$this->isApiResponseSuccess($res)) {
//            return BuyCardResponse::error($this->getApiResponseError($res));
//        }
//
//        $cards_res = $this->api()->getCard($provider, $amount, $res['VTCTransID']);
//
//        $cards = array_get($cards_res, 'ListCard') ?: [];
//        $cards = $this->parseCardsResponse($cards);
        $key_connection = $request->key_connection;
        $quantity = $request->quantity; 
        // load model
        $CI =& get_instance();
        $CI->load->model('Vtc_model');
        $response = $CI->Vtc_model->buycard($key_connection, $quantity);
        $buycard = $response['listCard'];
        $data = $response['data'];
        if($buycard && count($buycard) > 0) {
            $cards = [];
            for ($i = 0; $i < count($buycard); $i++) {
                $expired = new DateTime($buycard[$i]['exprireddate']);
                $formattedExpired = $expired->format('Y-m-d');
                $cards[] = new Card([
                    'code' => $buycard[$i]['code'],
                    'serial' => $buycard[$i]['serial'],
                    'expire' =>  $formattedExpired,
                ]);
            }

            return BuyCardResponse::success($cards, [
                'provider_tran_id' => $data['partnerTransID'],
                'provider_tran' => [],
                'balance' => $data['balance'],
            ]);
        } else {
            $sql = "UPDATE log_product_provider_request 
                SET provider_tran_id = ?,
                    balance = ?,
                    completed = ?,
                    status = 0,
                    output = ?,
                    error = ?
                WHERE request_id = ?";

        $CI->db->query($sql, [
            $data['partnerTransID'],
            $data['balance'],
            now(),
            json_encode(['listCard' => $data]),
            $data['description'],
            $request->request_id
        ]);
        }

        // // fake cards
        // $cards = [];
        // for ($i = 0; $i < $request->quantity; $i++) {
        //     $cards[] = new Card([
        //         'code' => random_string('numeric', 12),
        //         'serial' => random_string('numeric', 12),
        //         'expire' =>  '2018-12-31',
        //     ]);
        // }


        // return BuyCardResponse::success($cards, [
        //     'provider_tran_id' => random_string('numeric', 12),
        //     'provider_tran' => [],
        //     'balance' => rand(1000, 9999),
        // ]);
    }

    /**
     * Thanh toán hóa đơn
     */
    public function bill(BillRequest $request)
    {
        $CI =& get_instance();
        $CI->load->model('Vtc_model');
        $response = $CI->Vtc_model->payBill($request);
        if($response && count($response) > 0) {
            $partnerTransID = $response['data']['partnerTransID'];
            $balance = $response['data']['balance'];
            return BillResponse::success($response, [
                'provider_tran_id' => $partnerTransID,
                'provider_tran' => [],
                'balance' => $balance,
            ]);
        }
        // return BillResponse::success();
    }

    /**
     * Lay ma the
     *
     * @param GetCardRequest $request
     * @return GetCardResponse
     */
    public function getCard(GetCardRequest $request)
    {
//        $res = $this->api()->checkPartnerTransCode($request->request_id, 1);
//
//        if (!array_get($res, 'VTCTransID')) {
//            return GetCardResponse::error($this->getApiResponseError($res));
//        }
//
//        list($provider, $amount) = $request->parseKeyConnection(2);
//
//        if (!$provider || !$amount) {
//            return GetCardResponse::error('Param key_connection invalid');
//        }
//
//        $cards_res = $this->api()->getCard($provider, $amount, $res['VTCTransID']);
//
//        $cards = array_get($cards_res, 'ListCard') ?: [];
//        $cards = $this->parseCardsResponse($cards);
        $cards = [];
        for ($i = 0; $i < 5; $i++) {
            $cards[] = new Card([
                'code' => random_string('numeric', 12),
                'serial' => random_string('numeric', 12),
                'expire' => '2018-12-31',
            ]);
        }

        return GetCardResponse::success($cards);
    }

    /**
     * Phan tich danh sach cards lay tu api
     *
     * @param array $list
     * @return array
     */
    protected function parseCardsResponse(array $list)
    {
        $cards = [];

        foreach ($list as $row) {
            $cards[] = new Card([
                'code' => $row['CardCode'],
                'serial' => $row['CardSerial'],
                'expire' => $row['ExpriceDate'],
            ]);
        }

        return $cards;
    }

    /**
     * Lay thong tin giao dich
     *
     * @param FindTranRequest $request
     * @return FindTranResponse
     */
    public function findTran(FindTranRequest $request)
    {
        $type = $request->command == 'buy_card' ? 1 : 2;

        $res = $this->api()->checkPartnerTransCode($request->request_id, $type);

        if (!$this->isApiResponseSuccess($res)) {
            return new FindTranResponse([
                'status' => ProviderTranStatus::FAILED,
                'message' => $this->getApiResponseError($res),
            ]);
        }

        return new FindTranResponse([
            'status' => ProviderTranStatus::SUCCESS,
            'provider_tran_id' => $res['VTCTransID'],
            'provider_tran' => $res,
        ]);
    }

    /**
     * Nap tien dien thoai
     *
     * @param TopupMobileRequest $request
     * @return TopupMobileResponse 
     */
    public function topupMobile(TopupMobileRequest $request)
    {
        // list($provider, $amount) = $request->parseKeyConnection(2);

        // if (!$provider || !$amount) {
        //     return TopupMobileResponse::error('Param key_connection invalid');
        // }

        // $res = $this->api()->topupTelco($request->request_id, $provider, $request->account, $amount);

        // if (!$this->isApiResponseSuccess($res)) {
        //     return TopupMobileResponse::error($this->getApiResponseError($res));
        // }
       
        $CI =& get_instance();
        $CI->load->model('Vtc_model');
        $response = $CI->Vtc_model->topupMobile($request);
       
        if($response && count($response) > 0) {
            $partnerTransID = $response['data']['partnerTransID'];
            $balance = $response['data']['balance'];
            return TopupMobileResponse::success($response, [
                'provider_tran_id' => $partnerTransID,
                'provider_tran' => [],
                'balance' => $balance,
            ]);
            // return $book;
        }
        
    }

    /**
     * Nap tien dien thoai tra sau
     *
     * @param TopupMobilePostRequest $request
     * @return TopupMobilePostResponse
     */
    public function topupMobilePost(TopupMobilePostRequest $request)
    {
        // list($provider) = $request->parseKeyConnection(1);

        // if (!$provider) {
        //     return TopupMobilePostResponse::error('Param key_connection invalid');
        // }

        // $res = $this->api()->topupTelco($request->request_id, $provider, $request->account, $request->amount);

        // if (!$this->isApiResponseSuccess($res)) {
        //     return TopupMobilePostResponse::error($this->getApiResponseError($res));
        // }

        // return TopupMobilePostResponse::success([
        //     'provider_tran' => $res,
        //     'balance' => $res['PartnerBalance'],
        // ]);

        $CI =& get_instance();
        $CI->load->model('Vtc_model');
        $response = $CI->Vtc_model->topupMobilePost($request);
        if($response && count($response) > 0) {
            $partnerTransID = $response['data']['partnerTransID'];
            $balance = $response['data']['balance'];
            return TopupMobilePostResponse::success($response, [
                'provider_tran_id' => $partnerTransID,
                'provider_tran' => [],
                'balance' => $balance,
            ]);
        }
    }

    /**
     * Nap tien Korean
     *
     * @param TopupKoreanRequest $request
     * @return TopupKoreanResponse
     */
    public function topupKorean(TopupKoreanRequest $request)
    {
        $CI =& get_instance();
        $CI->load->model('Vtc_model');
        $response = $CI->Vtc_model->topupKorean($request);

        if($response && count($response) > 0) {
            $partnerTransID = $response['data']['partnerTransID'];
            $balance = $response['data']['balance'];
            return TopupKoreanResponse::success($response, [
                'provider_tran_id' => $partnerTransID,
                'provider_tran' => [],
                'balance' => $balance,
            ]);
        }
    }

    /**
     * Nap tien game
     *
     * @param TopupGameRequest $request
     * @return TopupGameResponse
     */
    public function topupGame(TopupGameRequest $request)
    {
//        list($provider, $amount) = $request->parseKeyConnection(2);
//
//        if (!$provider || !$amount) {
//            return TopupGameResponse::error('Param key_connection invalid');
//        }
//
//        $res = $this->api()->topupPartner($request->request_id, $provider, $request->account, $amount);
//
//        if (!$this->isApiResponseSuccess($res)) {
//            return TopupGameResponse::error($this->getApiResponseError($res));
//        }

        // return TopupGameResponse::success([
        //     'provider_tran_id' => random_string('numeric', 12),
        //     'provider_tran' => [],
        //     'balance' => rand(1000, 9999),
        // ]);
        $CI =& get_instance();

        $CI =& get_instance();
        $CI->load->model('Vtc_model');
        $response = $CI->Vtc_model->topupGame($request);
       
        if($response && count($response) > 0) {
            $partnerTransID = $response['data']['partnerTransID'];
            $balance = $response['data']['balance'];
            return TopupGameResponse::success($response, [
                'provider_tran_id' => $partnerTransID,
                'provider_tran' => [],
                'balance' => $balance,
            ]);
        }

    }

}