<?php

/**
 * Order
 */
$this->register('order', function ($order) {
    $body = macro('tpl::invoice_order/macros')->view($order->invoice_order);
    $body .= $this->macro->actions($order);

    return macro('mr::box')->box([
        'title' => lang('title_invoice_order_view'),
        'content' => $body,
    ]);
});


/**
 * Actions
 */
$this->register('actions', function ($order) {

    $CI = &get_instance();
    $CI->db->select('status');
    $CI->db->from('tran');
    $CI->db->where('invoice_id', $order->invoice_id);
    $tran_info = $CI->db->get()->row();
    $payment_success = false;
    if($tran_info && $tran_info->status == 'success') {
        $payment_success = true;
    }

    ob_start(); ?>

    <div class="btn-group btn-group-justified">

        <!-- <?php // if ($order->{'can:retake_cards'}): ?>

            <a href="" _url="<?php echo $order->{'adminUrl:retake_cards'}; ?>" class="btn btn-info verify_action"
               notice="<?php echo lang('notice_retake_cards'); ?>"
            ><?php // echo lang('button_retake_cards'); ?></a>

        <?php // endif ?> -->

        <?php if ($order->{'can:active'} && (int) $order->invoice_order->is_cron == 0 && $order->invoice_order->service_key !== 'ProductOrderTopupKorean') : ?>

            <!-- <a href="" _url="<?php echo $order->{'adminUrl:active'}; ?>" class="btn btn-info verify_action active_order"
               notice="<?php echo lang('notice_active_order'); ?>"
            ><?php echo lang('button_active'); ?></a> -->

          
    <a type="button"
       class="btn btn-info active_order"
       notice="<?php echo lang('notice_active_order'); ?>"
    ><?php echo lang('button_active'); ?></a>


        <?php endif ?>

        <script type="text/javascript">
            $(document).ready(function() {
                $('.active_order').on('click', function(e) {
                    e.preventDefault();
                    
                    var content = `
                        <div class="widget" id="verify_action">
                            <div class="title bg-primary">
                                <h6><i class="fa fa-exclamation-circle"></i> Thông báo</h6>
                            </div>

                            <div class="body">
                                <div id="notice">${$(this).attr('notice')}</div>
                                <div class="textC" style="margin-top:10px;">
                                    <button id="accept" class="btn btn-danger" style="margin: 5px;">
                                        <i class="fa fa-thumbs-o-up"></i> 
                                        <span class="btn-text">Chấp nhận</span>
                                        <span class="loading-text" style="display:none;">
                                            <i class="fa fa-spinner fa-spin"></i> Đang xử lý...
                                        </span>
                                    </button>
                                    <button id="cancel" class="btn" style="margin: 5px;" onclick="$.colorbox.close()">
                                        <i class="fa fa-times"></i> Hủy bỏ
                                    </button>
                                </div>
                            </div>

                            <div id="verify_action_load" class="form_load"></div>
                        </div>
                    `;

                    // Show ColorBox popup
                    $.colorbox({
                        html: content,
                        width: '500px',
                        closeButton: true,
                        overlayClose: true,
                        title: false,
                        overflow: "auto",
                    
                    });
                });
        // Update the click handler
        $(document).on('click', '#accept', async function(e) {
            e.preventDefault();
            
            // Disable button and show loading
            const $btn = $(this);
            $btn.prop('disabled', true);
            $btn.find('.btn-text').hide();
            $btn.find('.loading-text').show();

            try {
                console.log('Active order clicked');
                const services = ['provider_key', 'provider_key_2', 'provider_key_3'];
                const baseUrl = "<?php echo $order->{'adminUrl:active'}; ?>";

                for(let i = 0; i < services.length; i++) {
                    try {
                        console.log('Trying service:', services[i]);
                        const response = await $.ajax({
                            url: baseUrl,
                            type: 'POST',
                            data: {
                                provider_key: services[i]
                            },
                            dataType: 'json'
                        });
                        
                        if(response.status === true) {
                            window.location.reload();
                            return;
                        }
                        
                        if(i < services.length - 1) {
                            await new Promise(resolve => setTimeout(resolve, 2000));
                        }
                    } catch(error) {
                        console.log('Error with service ' + services[i] + ':', error);
                    }
                }

                window.location.reload();
            } catch (error) {
                // Re-enable button on error
                $btn.prop('disabled', false);
                $btn.find('.loading-text').hide();
                $btn.find('.btn-text').show();
                console.error('Error:', error);
            }
        });
    });
</script>

        <?php if ($order->{'can:cancel'} && $payment_success): ?>
            <!-- Huỷ bỏ -->
            <a href="" _url="<?php echo $order->{'adminUrl:cancel'}; ?>" class="btn btn-info verify_action cancel_order"
               notice="<?php echo lang('notice_confirm_action', ['action' => lang('button_cancel')]); ?>"
            ><?php echo lang('button_cancel'); ?></a>

        <?php endif ?>

        <?php if ($order->{'can:complete'} && $payment_success): ?>
            <!-- Hoàn thành -->
            <a href="" _url="<?php echo $order->{'adminUrl:complete'}; ?>" class="btn btn-info verify_action complete_order"
               notice="<?php echo lang('notice_confirm_action', ['action' => lang('button_complete')]); ?>"
            ><?php echo lang('button_complete'); ?></a>

        <?php endif ?>

    </div>

    <?php return ob_get_clean();
});

/**
 * Ghi chú đơn hàng từ Admin
 */
$this->register('admin_note', function ($invoice_order_id, $desc, $type = '', $status = '') {
    ob_start(); ?>
    <div class="portlet">
        <div class="portlet-heading dark">
            <div class="portlet-title">
                <h4>Ghi chú cho khách hàng</h4>
            </div>
            <div class="clearfix"></div>
        </div>
        <div class="panel-collapse collapse in">
            <div class="portlet-body no-padding">
                <textarea class="form-control" rows="4" id="admin_note"  maxlength="256" ><?php echo $desc; ?></textarea>
            </div>
        </div>
    </div>
    <div>
        <p>Thông tin trên sẽ được tự động gửi đến khách hàng khi bạn ấn nút "Hoàn thành" hoặc "Huỷ bỏ"</p>
        <button type="button" class="btn btn-primary" id="admin_note_btn">
            Gửi đến khách hàng
        </button>
    </div>
    <script>
        $('#admin_note_btn').click(() => {   
            var send_note = 1;
            var tran_status = '<?php echo $status; ?>';
            if(tran_status !== 'paid') {
                send_note = 0;
            }
            $.ajax({
                url: '<?= base_url('invoice_order/update_send_note') ?>',
                method: 'POST',
                data: {invoice_order_id: <?php echo $invoice_order_id; ?>, send_note: send_note, type: '<?php echo $type; ?>'},    
                success: (data) => {
                    // console.log('ajax data', data);
                    if (data.status) {
                        if(tran_status !== 'paid') {
                            alert('Không gửi đến khách hàng vì đơn hàng chưa thanh toán');
                        } else {
                            alert('Gửi thành công');
                        }
                    } else {
                        alert('Gửi thất bại');
                    }
                }
            });
        });

        // $('#admin_note_card_btn').click(() => {   
        //     $.ajax({
        //         url: '<?= base_url('invoice_order/update_send_note') ?>',
        //         method: 'POST',
        //         data: {invoice_order_id: <?php echo $invoice_order_id; ?>},    
        //         success: (data) => {
        //             // console.log('ajax data', data);
        //             if (data.status) {
        //                 alert('Gửi thành công');
        //             } else {
        //                 alert('Gửi thất bại');
        //             }
        //         }
        //     });
        // });
        // $('#admin_note_btn').click(() => {
        $(document).on('input', '#admin_note', function () {  
            let note = $('#admin_note').val();

            // if (note.length === 0) {
            //     return alert('Ghi chú rỗng');
            // }

            $.ajax({
                url: '<?= base_url('invoice_order/update_admin_note') ?>',
                method: 'POST',
                data: {note: note, invoice_order_id: <?php echo $invoice_order_id; ?>, type: '<?php echo $type; ?>'},    
                success: (data) => {
                    console.log('ajax data', data);
                    if (data.status) {
                        // alert('Ghi chú đã được cập nhật thành công');
                    } else {
                        alert('Ghi chú cập nhật thất bại');
                    }
                }
            });
        });
    </script>
    <?php return ob_get_clean();
});

/**
 * Ghi chú đơn hàng từ Admin
 */
$this->register('admin_note_card', function ($invoice_order_id, $status = '') {
    ob_start(); ?>
    <div>
        <p>Thông tin trên sẽ được tự động gửi đến khách hàng khi bạn ấn nút "Hoàn thành" hoặc "Huỷ bỏ"</p>
        <button type="button" class="btn btn-primary" id="admin_note_card_btn">
            Gửi đến khách hàng
        </button>
    </div>
    <script>
        $('#admin_note_card_btn').click(() => {   
            var send_note = 1;
            var tran_status = '<?php echo $status; ?>';
            if(tran_status !== 'paid') {
                send_note = 0;
            }
            $.ajax({
                url: '<?= base_url('invoice_order/update_send_note') ?>',
                method: 'POST',
                data: {invoice_order_id: <?php echo $invoice_order_id; ?>, send_note: send_note},    
                success: (data) => {
                    // console.log('ajax data', data);
                    if (data.status) {
                        if(tran_status !== 'paid') {
                            alert('Không gửi đến khách hàng vì đơn hàng chưa thanh toán');
                        } else {
                            alert('Gửi thành công');
                        }
                    } else {
                        alert('Gửi thất bại');
                    }
                }
            });
        });
        $(document).on('input', '.product_card_note', function () {    
            let notes = [];
            $('#product_card_table .product_card_note').each(function() {
                notes.push({
                    id: $(this).attr('data-id'),
                    sort: $(this).attr('data-sort') ? $(this).attr('data-sort') : 0,
                    note: $(this).val()
                });
            });
            // console.log('notes', notes);
            // if (notes.length === 0) {
            //     return alert('Không có ghi chú nào được tìm thấy');
            // }
            $.ajax({
                url: '<?= base_url('invoice_order/update_admin_card_note') ?>',
                method: 'POST',
                data: {notes: notes, invoice_order_id: <?php echo $invoice_order_id; ?>},    
                success: (data) => {
                    // console.log('ajax data', data);
                    if (data.status) {
                        // alert('Ghi chú đã được cập nhật thành công');
                    } else {
                        alert('Ghi chú cập nhật thất bại');
                    }
                }
            });
        });

        // Đổi trạng thái đơn hàng thành đang xử lý
        $("#change_status_order_processing").click(() => {
            $.ajax({
                url: '<?= base_url('invoice_order/change_status_order_processing') ?>',
                method: 'POST',
                data: {invoice_order_id: <?php echo $invoice_order_id; ?>},
                success: (data) => {
                    console.log('ajax data', data);
                    if (data.status) {
                        alert('Trạng thái đơn hàng đã được cập nhật thành công');
                        window.location.reload();
                    } else {
                        alert('Cập nhật trạng thái đơn hàng thất bại');
                    }
                }
            });
        });

        function debounce(func, wait) {
            let timeout;
            return function () {
                clearTimeout(timeout);
                timeout = setTimeout(func, wait);
            };
        }

        function getCardDataFromTable() {
            const data = [];

            $('tr.card_item').each(function () {
                const $row = $(this);
                const codeInput = $row.find('.product_card_code');
                const serialInput = $row.find('.product_card_serial');
                const expireInput = $row.find('.product_card_expire');
                const noteInput = $row.find('.product_card_tmp_note');

                data.push({
                    id: Number(codeInput.data('id')),
                    sort: Number(codeInput.data('sort')),
                    code: codeInput.val(),
                    serial: serialInput.val(),
                    expire: expireInput.val(),
                    note: noteInput.val(),
                });
            });

            return data;
        }

        $(document).on('input', '.change_data_card', function () {            // Debounce function bạn đã định nghĩa từ trước
            debounce(function () {
                const updatedData = getCardDataFromTable();

                $.ajax({
                    url: '<?= base_url('invoice_order/update_preorder_card_data') ?>',
                    method: 'POST',
                    data: {data_preorder_card: updatedData, invoice_order_id: <?php echo $invoice_order_id; ?>},    
                    success: (data) => {
                        // console.log('ajax data', data);
                        if (data.status) {
                            // alert('Dữ liệu thẻ đã được cập nhật thành công');
                        } else {
                            alert('Cập nhật dữ liệu thẻ thất bại');
                        }
                    }
                });
            }, 1000)(); // 300ms delay
        });
    </script>
    <?php return ob_get_clean();
});

/**
 * Log provider request
 */
$this->register('log_provider_request', function ($list) {
    ob_start(); ?>

    <table class="table table-bordered table-hover">

        <thead>
        <th><?php echo lang('provider'); ?></th>
        <th><?php echo lang('request_id'); ?></th>
        <th><?php echo lang('status'); ?></th>
        <th><?php echo lang('provider_tran_id'); ?></th>
        <th><?php echo lang('error'); ?></th>
        <th><?php echo lang('time'); ?></th>
        </thead>

        <tbody>

        <?php foreach ($list as $row): ?>
            <tr>
                <td><?php echo $row->provider ? $row->provider->name : $row->provider_key; ?></td>
                <td><?php echo $row->request_id; ?></td>
                <td><?php echo macro()->status_color($row->status ? 'success' : 'failed'); ?></td>
                <td><?php echo $row->provider_tran_id; ?></td>
                <td><?php echo $row->error; ?></td>
                <td><?php echo $row->{'format:created,full'}; ?></td>
            </tr>
        <?php endforeach ?>

        </tbody>

    </table>

    <?php $body = ob_get_clean();

    return macro('mr::box')->box([
        'title' => lang('title_list_log_provider_requests'),
        'content' => $body,
    ]);
});

/**
 * Log activities
 */
$this->register('deposit_log_activities', function ($invoice_order_id) {
    $CI = &get_instance();
    $CI->db->select('*');
    $CI->db->from('log_activity');
    $CI->db->where('key', $invoice_order_id);
    $CI->db->where('logger_key', 'DepositInvoice');
    $list = $CI->db->get()->result();
    ob_start(); ?>
   <table class="table table-bordered table-hover">

        <thead>
        <th><?php echo lang('action'); ?></th>
        <th>Thực hiện bởi</th>
        <th><?php echo lang('time'); ?></th>
        </thead>

        <tbody>

        <?php foreach ($list as $row): 
            $CI->db->select(', name');
            $CI->db->from('admin');
            $CI->db->where('id', $row->owner_key);
            $admin_info = $CI->db->get()->row();
            ?>
            <tr>
                <td><?php echo lang($row->action); ?></td>
                <td><?php echo $admin_info->name; ?></td>
                <td><?php echo date('d/m/Y H:i:s', $row->created); ?></td>
            </tr>
        <?php endforeach ?>

        </tbody>

    </table>
    <?php $body = ob_get_clean();
    return macro('mr::box')->box([
        'title' => lang('title_list_log_activities'),         
        'content' => $body,
    ]);
});
