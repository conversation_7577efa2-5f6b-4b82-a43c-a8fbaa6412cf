<?php

use App\Product\Model\OrderModel;
use App\Product\Model\ProductModel;

/**
 * Get Korean top-up product type name based on language
 */
function getKoreanTopupTypeName($productType) {
    switch($productType) {
        case 1:
            return lang('topup_korean_regular_phone');
        case 2:
            return lang('topup_korean_special_phone');
        case 3:
            return lang('topup_korean_data_package');
        default:
            return '';
    }
}

/**
 * Get Korean top-up package type name based on language
 */
function getKoreanPackageTypeName($packageType) {
    switch($packageType) {
        case 1:
            return lang('topup_korean_regular_package');
        case 2:
            return lang('topup_korean_monthly_package');
        default:
            return '';
    }
}

// Thanh toán hóa đơn
$this->register('ProductOrderBill', function (array $args) {
    ob_start(); ?>
    <?php
    $invoice = $args['invoice'];

    $table = [];


    $table['columns'] = [
        'id' => lang('transaction_code'),
        'order_number' => lang('order_number'),
        'service_key' => lang('type'),
        'customer_code' => lang('customer_code'),
        'amount' => lang('bill_total'),
        'order_status' => lang('order_status'),
        'must_pay' => lang('must_pay'),
        'created' => lang('created'),
        'note' => lang('note'),   
    ];

    $table['rows'] = [];
    $CI = &get_instance();
    foreach ($invoice->invoice_orders as $row) {
        $CI->db->select('send_note');
        $CI->db->from('product_order');
        $CI->db->where('invoice_order_id', $row->id);
        $po_info = $CI->db->get()->row();

        $is_send_note = $po_info->send_note == '1' ? true : false;
        if(($row->order_status == 'completed' || $row->order_status == 'canceled') && $row->invoice_status == 'paid'){
            $is_send_note = true;
        }
        $note = $is_send_note ? $row->desc : '' ;

        $table['rows'][] = [
            'id' => $row->id,
            'order_number' => $row->invoice_id,
            'service_key' => $row->service_name . ' <br /> ' . $row->order_options['product_name'],
            'customer_code' => $row->order_options['account'],
            'amount' => $row->{'format:amount'},
            'order_status' => macro()->status_color($row->order_status, $row->order_status_name),
            'must_pay' => currency_convert_format_amount($row->amount, get_current_currency_id()),
            'created' => $row->{'format:created,time'}, 
            'note' => $note,
        ];
    }

    echo macro('mr::table')->table($table);
    ?>

    <?php
    return ob_get_clean();
});
// Nạp tiền
$this->register('DepositPayment', function (array $args) {
    ob_start(); ?>
    <?php
    $invoice = $args['invoice'];

    $table = [];


    $table['columns'] = [
        'id' => lang('transaction_code'),
        'order_number' => lang('order_number'),
        'service_key' => lang('product_name'),
        'order_status' => lang('order_status'),
        'total_pay' => lang('total_pay'),
        'created' => lang('created'),
        'note' => lang('note'),
        'action' => lang('action'),
    ];

    $table['rows'] = [];

    foreach ($invoice->invoice_orders as $row) {
        $invoice_order_id = $row->id;
        $order = OrderModel::findWhere([
            'invoice_order_id' => $invoice_order_id
        ]);
 

        $table['rows'][] = [
            'id' => $row->id,
            'order_number' => $row->invoice_id,
            'service_key' => $row->service_name,
            'order_status' => macro()->status_color($row->order_status, $row->order_status_name),
            'total_pay' => currency_convert_format_amount($row->amount, get_current_currency_id()),
            'created' => $row->{'format:created,time'},
            'note' => ($row->send_note == '1' || $row->invoice_status == 'paid')? $row->desc : '',
            'action' => t('html')->a($row->{'url:view'}, lang('button_view'), ['class' => 'btn btn-success btn-xs']),
        ];
    }

    echo macro('mr::table')->table($table);
    ?>

    <?php
    return ob_get_clean();
});

// Mua mã thẻ cào
$this->register('ProductOrderCard', function (array $args) {
    ob_start(); 

    $invoice = $args['invoice'];
    $tran_status = $args['tran_status'];
    if(!isset($_GET['method'])){
        $CI = &get_instance();

        $CI->db->select('po.*, p.provider_key');
        $CI->db->from('product_order po');
        $CI->db->join('product p', 'p.id = po.product_id', 'left');
        $CI->db->where('po.invoice_id', $invoice->id);
        $CI->db->order_by('status', 'ASC');
        $po = $CI->db->get()->result();

        // echo "<pre>";
        // print_r($po);
        // echo "</pre>";
        // die;

        $cards = [];
        $cards_pending = [];

        // Loop danh sách product_order
        foreach ($po as $row) {
            $CI->db->select('io.invoice_status, io.order_status');
            $CI->db->from('invoice_order io');  
            $CI->db->where('io.id', $row->invoice_order_id);
            $io = $CI->db->get()->row();

            $data_notes = json_decode($row->data_notes, true);
            $data_preorder_card = json_decode($row->data_preorder_card, true);

            $is_send_note = $row->send_note == '1' ? true : false;
            if(($io->order_status == 'completed' || $io->order_status == 'canceled') && $io->invoice_status == 'paid'){
                $is_send_note = true;
            }

            if($row->provider_key == 'PreOrder' && $io->order_status == 'completed') {
                // Trường hợp là PreOrder
                foreach($data_preorder_card as $i => $row_card) {
                    $cards[] = [
                        'id' => 0,
                        'product_order_id' => $row->id,
                        'code' => security_encrypt($row_card['code'], 'encode'),
                        'serial' => $row_card['serial'],
                        'expire' => $row_card['expire'],
                        'created' => '',
                        'card_status' => 'completed',
                        'product_id' => $row->product_id,
                        'product_order_quantity' => $row->quantity,
                        'card_note' => isset($row_card['note']) && $is_send_note ? $row_card['note'] : '',
                    ];
                }
            } else {
                
                // Các nhà cung cấp khác hoặc PreOrder nhưng đơn hàng chưa hoàn thành
                // Đơn hoàn thành
                if($row->status == 'completed'){
                    $CI->db->select('product_order_cards.*, product_order.product_id, product_order.quantity as product_order_quantity');
                    $CI->db->from('product_order');
                    $CI->db->join('product_order_cards', 'product_order.id = product_order_cards.product_order_id', 'left');
                    $CI->db->where('product_order.id', $row->id);
                    $poc_cards = $CI->db->get()->result_array();
                    if($poc_cards){
                        if(count($poc_cards) < $row->quantity){
                            $cards = array_merge($cards, $poc_cards);
                            $lost_item = (int)$row->quantity - count($poc_cards);
                            for ($i = count($poc_cards); $i < $lost_item; $i++) {
                                $card_status = $row->status;
                                if($card_status == 'canceled'){
                                    if($tran_status == 'success'){
                                        $card_status = 'refunded';
                                    }
                                }
                                $card_note = isset($data_notes[$i]) ? $data_notes[$i]['note'] : '';
                                if($is_send_note){
                                    $card_note = $card_note ? $card_note : '';
                                }
                                $cards_pending[] = [
                                    'id' => 0,
                                    'product_order_id' => $row->id,
                                    'code' => '',
                                    'code_encode' => '',
                                    'serial' => '',
                                    'expire' => '',
                                    'created' => '',
                                    'card_status' => $card_status,
                                    'product_id' => $row->product_id,
                                    'product_order_quantity' => $row->quantity,
                                    'card_note' => $card_note,
                                ];
                            }

                        } else {
                            for($k = 0; $k < count($poc_cards); $k++){
                                $card_note = $poc_cards[$k]['card_note'] ? $poc_cards[$k]['card_note'] : (isset($data_notes[$k]) ? $data_notes[$k]['note'] : '');
                                $card_note = $data_preorder_card[$k]['note'] ? $data_preorder_card[$k]['note'] : $card_note;
                                if($is_send_note){ 
                                    $card_note = $card_note ? $card_note : '';
                                }
                                $poc_cards[$k]['card_note'] = $card_note;
                            }
                            $cards = array_merge($cards, $poc_cards);
                        }
                    }
                } else {
                    
                    // Các đơn khác
                    for ($i = 0; $i < (Int)$row->quantity; $i++) {
                        $card_status = $row->status;
                        if($card_status == 'canceled'){
                            if($tran_status == 'success'){
                                $card_status = 'refunded';
                            }
                        }
                        $card_note = isset($data_notes[$i]) ? $data_notes[$i]['note'] : '';
                        $card_note = $data_preorder_card[$i]['note'] ? $data_preorder_card[$i]['note'] : $card_note;
                        
                        $card_note = $is_send_note ? $card_note : '';
                   
                        $cards_pending[] = [
                            'id' => 0,
                            'product_order_id' => $row->id,
                            'code' => '',
                            'code_encode' => '',
                            'serial' => '',
                            'expire' => '',
                            'created' => '',
                            'card_status' => $card_status,
                            'product_id' => $row->product_id,
                            'product_order_quantity' => $row->quantity,
                            'card_note' => $card_note,
                        ];
                    }
                }
            }
        }

        $totalCardClipboard = "";
        if (!empty($cards)) {
            $stt = 1;
            foreach ($cards as $row) {
                if(!empty($row['serial'])){
                    $tenthe = ProductModel::find($row['product_id'])->name;
                    // $totalCardClipboard .= ($stt) . '    ' . $tenthe . '    ' . strtoupper(security_encrypt($row['code'], 'decode')) . '   ' . strtoupper($row['serial']) . '    ' . $row['expire'] . "\n";
                
                    $totalCardClipboard .= $stt . '. ' . $tenthe . "\n Seri: " . strtoupper(security_encrypt($row['code'], 'decode')) . "\n Mã thẻ: " . strtoupper($row['serial']) . "\n\n";
                }
                $stt++;
            }
            ?>
            <button class="btn-copy btn-default mb-4 cursor-pointer" copy-data='<?= $totalCardClipboard ?>'>
                <i class="fa fa-copy"></i>
                <?= lang('copy_all_card_clipboard') ?>
            </button>
            <?php
        }

        

        $stt = 1;
        ?>

        <!-- Mã thẻ hoàn thành -->
        <?php if(!empty($cards)){ ?>
        <h6
                class="text-base font-bold tracking-tight md:text-lg text-[#1434CB] mb-4">
            <?php echo lang('list_card_completed'); ?>
        </h6>
        <table class="table-responsive w-full p-3 text-left text-sm text-gray-500 md:text-sm rtl:text-right">
            <thead class="bg-gray-50 text-gray-700">
            <tr>
                <th scope="col" class="border-r border-gray-200 p-1 md:border-transparent md:px-6 md:py-3">STT</th>
                <th scope="col"
                    class="border-r border-gray-200 p-1 md:border-transparent md:px-6 md:py-3"><?php echo lang('product_name'); ?></th>
                    <th scope="col" class="w-1/3 min-w-fit border-r border-gray-200 p-2 md:border-transparent md:px-6 md:py-3 text-[#f00]">
                    <?php echo lang('card_code'); ?>
                </th>
                <th scope="col"
                    class="border-r border-gray-200 p-2 md:border-transparent md:px-6 md:py-3"><?php echo lang('card_serial'); ?></th>
                <th scope="col"
                    class="border-r border-gray-200 p-2 md:border-transparent md:px-6 md:py-3"><?php echo lang('card_expire'); ?></th>
               
                <th scope="col"
                    class="border-r border-gray-200 p-2 md:border-transparent md:px-6 md:py-3"><?php echo lang('note'); ?></th>
            </tr>
            </thead>
            <tbody>
            <?php foreach ($cards as $row): ?>
                <tr class="border-b border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800 md:border-transparent">

                    <th scope="row"
                        class="whitespace-nowrap border-r border-gray-200 p-1 font-medium text-black md:border-transparent md:px-6 md:py-4"
                    >
                        <?php echo $stt++ ?>
                    </th>

                    <th scope="row"
                        class="whitespace-nowrap border-r border-gray-200 p-1 font-medium text-black md:border-transparent md:px-6 md:py-4"
                    >
                        <?= ProductModel::find($row['product_id'])->name ?>
                    </th>

                    <th scope="row"
                        class="whitespace-nowrap border-r border-gray-200 p-1 font-medium md:border-transparent md:px-6 md:py-4 text-[#f00]"
                    >
                        <?php echo strtoupper(security_encrypt($row['code'], 'decode')); ?>
                        <?php if(!empty($row['code'])): ?>
                            <button class='copy-btn inline-block' copy-data='<?= strtoupper(security_encrypt($row['code'], 'decode')); ?>'></button>
                        <?php endif; ?>
                    </th>
                    <th scope="row"
                        class="whitespace-nowrap border-r border-gray-200 p-1 font-medium text-black md:border-transparent md:px-6 md:py-4"
                    >
                        <?php echo strtoupper($row['serial']); ?>
                        <?php if(!empty($row['serial'])): ?>
                            <button class='copy-btn inline-block' copy-data='<?= $row['serial'] ?>'></button>
                        <?php endif; ?>
                    </th>
                    <th scope="row"
                        class="whitespace-nowrap border-r border-gray-200 p-1 font-medium text-black md:border-transparent md:px-6 md:py-4"
                    >
                        <?php echo($row['expire']); ?>
                    </th>
                    <th scope="row"
                        class="whitespace-nowrap border-r border-gray-200 p-1 font-medium text-black md:border-transparent md:px-6 md:py-4"
                    >
                        <?php echo($row['card_note'] ? $row['card_note'] : ''); ?>
                    </th>
                </tr>
            <?php endforeach ?>
            </tbody>
        </table>
        <?php } ?>

        <!-- Mã thẻ đang xử lý -->
         <?php if(!empty($cards_pending)){ ?>
        <h6
                class="text-base font-bold tracking-tight md:text-lg text-[#1434CB] mb-4">
            <?php echo lang('list_card_processing'); ?>
        </h6>
        <table class="table-responsive w-full p-3 text-left text-sm text-gray-500 md:text-sm rtl:text-right">
            <thead class="bg-gray-50 text-gray-700">
            <tr>
                <th scope="col" class="border-r border-gray-200 p-1 md:border-transparent md:px-6 md:py-3">STT</th>
                <th scope="col"
                    class="border-r border-gray-200 p-1 md:border-transparent md:px-6 md:py-3"><?php echo lang('product_name'); ?></th>
                    <th scope="col"
                    class="border-r border-gray-200 p-2 md:border-transparent md:px-6 md:py-3"><?php echo lang('order_status'); ?></th>
                    <th scope="col"
                    class="border-r border-gray-200 p-2 md:border-transparent md:px-6 md:py-3"><?php echo lang('note'); ?></th>
            </tr>
            </thead>
            <tbody>
            <?php foreach ($cards_pending as $row): ?>
                <tr class="border-b border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800 md:border-transparent">

                    <th scope="row"
                        class="whitespace-nowrap border-r border-gray-200 p-1 font-medium text-black md:border-transparent md:px-6 md:py-4"
                    >
                        <?php echo $stt++ ?>
                    </th>

                    <th scope="row"
                        class="whitespace-nowrap border-r border-gray-200 p-1 font-medium text-black md:border-transparent md:px-6 md:py-4"
                    >
                        <?= ProductModel::find($row['product_id'])->name ?>
                    </th>

                    <th scope="row"
                        class="whitespace-nowrap border-r border-gray-200 p-1 font-medium text-black md:border-transparent md:px-6 md:py-4"
                    >
                        <?php echo macro()->status_color($row['card_status'], lang('order_status_'.$row['card_status'])) ?>
                    </th> 
                    <th scope="row"
                        class="whitespace-nowrap border-r border-gray-200 p-1 font-medium text-black md:border-transparent md:px-6 md:py-4"
                    >
                        <?php echo($row['card_note'] ? $row['card_note'] : ''); ?>
                    </th>
                </tr>
            <?php endforeach ?>
            </tbody>
        </table>
        <?php }
    } else {
        // disable withdraw payment
        $invoice = $args['invoice'];

        $table = [];


        $table['columns'] = [
            'action' => lang('action'),
            'id' => lang('transaction_code'),
            'order_number' => lang('order_number'),
            'service_key' => lang('product_name'),
            'desc' => lang('desc'),
            'quantity' => lang('qty'),
            'order_status' => lang('order_status'),
            'must_pay' => lang('must_pay'),
            'note' => lang('note'),
            'created' => lang('created'),  
        ];

        $table['rows'] = [];

        foreach ($invoice->invoice_orders as $row) {

            $invoice_order_id = $row->id;
            $order = OrderModel::findWhere([
                'invoice_order_id' => $invoice_order_id
            ]);
            $product = ProductModel::find($row->order_options['product_id']);
            if (!isset($product)) {
                continue;
            }


            $table['rows'][] = [
                'action' => t('html')->a($row->{'url:view'}, lang('button_view'), ['class' => 'btn btn-success btn-xs']),
                'id' => $row->id,
                'order_number' => $order->invoice_id,
                'service_key' => $row->service_name . ' <br /> ' . $row->order_options['product_name'],
                'desc' => number_format($product->price_par) . 'đ',
                'quantity' => number_format($row->order_options['quantity']),
                'order_status' => macro()->status_color($row->order_status, $row->order_status_name),
                'must_pay' => currency_convert_format_amount($row->amount, get_current_currency_id()),
                'note' => $row->desc,
                'created' => $row->{'format:created,time'},
            ];
        }

        echo macro('mr::table')->table($table);
    }
    return ob_get_clean();
});
// Nạp game
$this->register('ProductOrderTopupGame', function (array $args) {
    ob_start();

    $invoice = $args['invoice'];

    $table = [];


    $table['columns'] = [
        'id' => lang('transaction_code'),
        'action' => lang('action'),
        'order_number' => lang('order_number'),
        'service_key' => lang('type'),
        'game_account' => lang('game_account'),
        'order_status' => lang('order_status'),
        'created' => lang('created'),
        'note' => lang('note'),  
        
    ];

    $table['rows'] = [];
    $CI = &get_instance();
    foreach ($invoice->invoice_orders as $row) {
       
        $CI->db->select('send_note');
        $CI->db->from('product_order');
        $CI->db->where('invoice_order_id', $row->id);
        $po_info = $CI->db->get()->row();

        $is_send_note = $po_info->send_note == '1' ? true : false;
        if(($row->order_status == 'completed' || $row->order_status == 'canceled') && $row->invoice_status == 'paid'){
            $is_send_note = true;
        }
        $note = $is_send_note ? $row->desc : '' ;

        $table['rows'][] = [
            'id' => $row->id,
            'action' => t('html')->a($row->{'url:view'}, lang('button_view'), ['class' => 'btn btn-success btn-xs']),
            'order_number' => $row->invoice_id,
            'service_key' => $row->service_name . ' <br /> ' . $row->order_options['product_name'],
            'game_account' => $row->order_options['account'] . ' <br /> ' . $row->order_options['description'],
            'order_status' => macro()->status_color($row->order_status, $row->order_status_name),
            'must_pay' => currency_convert_format_amount($row->amount, get_current_currency_id()),
            'created' => $row->{'format:created,time'},
            'note' => $note,
        ];
    }

    echo macro('mr::table')->table($table);
    return ob_get_clean();
});

// Nạp điện thoại
$this->register('ProductOrderTopupMobile', function (array $args) {
    ob_start();

    $invoice = $args['invoice'];

    $table = [];


    $table['columns'] = [
        'id' => lang('transaction_code'),
        'order_number' => lang('order_number'),
        'service_key' => 'Hình thức',
        'network' => 'Mạng',
        'account' => 'Số điện thoại',
        'amount' => 'Số tiền nạp',
        'price' => 'Thanh toán',
        'date' => 'Ngày tạo',
        'note' => lang('note'),  
    ];

    $table['rows'] = [];
    $index = 1;
    $CI = &get_instance();
    foreach ($invoice->invoice_orders as $row) {
        $CI->db->select('send_note');
        $CI->db->from('product_order');
        $CI->db->where('invoice_order_id', $row->id);
        $po_info = $CI->db->get()->row();

        $is_send_note = $po_info->send_note == '1' ? true : false;
        if(($row->order_status == 'completed' || $row->order_status == 'canceled') && $row->invoice_status == 'paid'){
            $is_send_note = true;
        }
        $note = $is_send_note ? $row->desc : '' ;


        $table['rows'][] = [
            'id' => $row->id,
            'order_number' => $row->invoice_id,
            'service_key' => $row->service_name,
            'network' => isset($row->order_options['network_name']) ? $row->order_options['network_name'] : '',
            'account' => $row->order_options['account'],
            'amount' => currency_convert_format_amount($row->amount, get_current_currency_id()),
            'price' => currency_convert_format_amount($row->amount, get_current_currency_id()),
            'date' => $row->{'format:created,time'},
            'note' => $note,
        ];
        $index++;
    }

    echo macro('mr::table')->table($table);
    return ob_get_clean();
});
// Nạp thẻ hàn quốc
$this->register('ProductOrderTopupKorean', function (array $args) {
    ob_start();

    $invoice = $args['invoice'];

    $table = [];


    $table['columns'] = [
        'id' => lang('transaction_code'),
        'order_number' => lang('order_number'),
        'service_key' => 'Hình thức',
        'network' => 'Mạng',
        'account' => 'Số điện thoại',
        'amount' => 'Số tiền nạp',
        'price' => 'Thanh toán',
        'date' => 'Ngày tạo',
        'note' => lang('note'),  
    ];

    $table['rows'] = [];
    $index = 1;
    $CI = &get_instance();
    foreach ($invoice->invoice_orders as $row) {
        $CI->db->select('send_note');
        $CI->db->from('product_order');
        $CI->db->where('invoice_order_id', $row->id);
        $po_info = $CI->db->get()->row();

        $is_send_note = $po_info->send_note == '1' ? true : false;
        if(($row->order_status == 'completed' || $row->order_status == 'canceled') && $row->invoice_status == 'paid'){
            $is_send_note = true;
        }
        $note = $is_send_note ? $row->desc : '' ;

        $table['rows'][] = [
            'id' => $row->id,
            'order_number' => $row->invoice_id,
            'service_key' => $row->service_name,
            'network' => isset($row->order_options['network_name']) ? $row->order_options['network_name'] : '',
            'account' => $row->order_options['account'] . ' <br /> ' .
                        (isset($row->order_options['package_type']) ? getKoreanPackageTypeName($row->order_options['package_type']) : '') . ' <br /> ' .
                        (isset($row->order_options['product_type']) && $row->order_options['package_type'] == 1  ? lang('topup_korean_product_type').': '.getKoreanTopupTypeName($row->order_options['product_type']) : '') ,
            'amount' => currency_convert_format_amount($row->amount, get_current_currency_id()),
            'price' => currency_convert_format_amount($row->amount, get_current_currency_id()),
            'date' => $row->{'format:created,time'},
            'note' => $note,
        ];
        $index++;
    }

    echo macro('mr::table')->table($table);
    return ob_get_clean();
});
// nạp trả sau
$this->register('ProductOrderPaylate', function (array $args) {
    ob_start();

    $invoice = $args['invoice'];

    $table = [];


    $table['columns'] = [
        'id' => lang('transaction_code'),
        'order_number' => lang('order_number'),
        'service_key' => 'Hình thức',
        'network' => 'Mạng',
        'account' => 'Số điện thoại',
        'amount' => 'Số tiền nạp',
        'price' => 'Thanh toán',
        'date' => 'Ngày tạo',
        'note' => lang('note'),  
    ];

    $table['rows'] = [];
    $index = 1;
    $CI = &get_instance();
    foreach ($invoice->invoice_orders as $row) {
        $CI->db->select('send_note');
        $CI->db->from('product_order');
        $CI->db->where('invoice_order_id', $row->id);
        $po_info = $CI->db->get()->row();

        $is_send_note = $po_info->send_note == '1' ? true : false;
        if(($row->order_status == 'completed' || $row->order_status == 'canceled') && $row->invoice_status == 'paid'){
            $is_send_note = true;
        }
        $note = $is_send_note ? $row->desc : '' ;


        $table['rows'][] = [
            'id' => $row->id,
            'order_number' => $row->invoice_id,
            'service_key' => $row->service_name,
            'network' => isset($row->order_options['network_name']) ? $row->order_options['network_name'] : '',
            'account' => $row->order_options['account'],
            'amount' => currency_convert_format_amount($row->amount, get_current_currency_id()),
            'price' => currency_convert_format_amount($row->amount, get_current_currency_id()),
            'date' => $row->{'format:created,time'},
            'note' => $note,
        ];
        $index++;
    }

    echo macro('mr::table')->table($table);
    return ob_get_clean();
});
?>
